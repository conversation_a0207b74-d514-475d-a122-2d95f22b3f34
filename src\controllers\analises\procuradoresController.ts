import { Request, Response } from 'express';
import prisma from '../../dbClient';

// Endpoint para listar processos com despacho de publicação
export const listarProcessosPublicados = async (req: Request, res: Response): Promise<any> => {
  try {
    const { ano, dataInicio, dataFim, page = 1, limit = 200 } = req.query;
    const skip = (Number(page) - 1) * Number(limit);
    
    let dataFilter: any = {};
    
    if (ano) {
      const anoNum = Number(ano);
      dataFilter = {
        gte: new Date(`${anoNum}-01-01T00:00:00.000Z`),
        lt: new Date(`${anoNum + 1}-01-01T00:00:00.000Z`)
      };
    } else if (dataInicio && dataFim) {
      dataFilter = {
        gte: new Date(dataInicio as string),
        lte: new Date(dataFim as string)
      };
    } else {
      return res.status(400).json({
        error: 'É necessário fornecer o ano ou o período (dataInicio e dataFim)'
      });
    }

    const whereClause = {
      nome: {
        contains: 'Publicação de pedido de registro para oposição',
        mode: 'insensitive' as const
      },
      rpi: {
        dataPublicacao: dataFilter,
        numero: {
          lte: 9999 // Excluir RPIs de teste
        }
      }
    };

    const processos = await prisma.despacho.findMany({
      where: whereClause,
      select: {
        id: true,
        codigo: true,
        nome: true,
        processo: {
          select: {
            id: true,
            numero: true,
            procurador: {
              select: {
                id: true,
                nome: true
              }
            },
            marca: {
              select: {
                id: true,
                nome: true,
                ncl: {
                  select: {
                    codigo: true
                  }
                }
              }
            }
          }
        },
        rpi: {
          select: {
            id: true,
            numero: true,
            dataPublicacao: true
          }
        }
      },
      take: Number(limit),
      skip: skip,
      orderBy: {
        rpi: {
          dataPublicacao: 'desc'
        }
      }
    });

    const totalRegistros = await prisma.despacho.count({
      where: whereClause // Reutiliza a mesma cláusula where
    });

    const resultado = processos.map(despacho => ({
      numeroProcesso: despacho.processo.numero,
      nomeDespacho: despacho.nome,
      dataDespacho: despacho.rpi.dataPublicacao,
      numeroRPI: despacho.rpi.numero,
      procurador: despacho.processo.procurador?.nome || 'Não informado',
      nomeMarca: despacho.processo.marca?.nome || 'Não informado',
      classe: despacho.processo.marca?.ncl[0]?.codigo || 'Não informado'
    }));

    return res.status(200).json({
      data: resultado,
      pagination: {
        total: totalRegistros,
        page: Number(page),
        limit: Number(limit),
        pages: Math.ceil(totalRegistros / Number(limit))
      }
    });
  } catch (error) {
    console.error('Erro ao listar processos publicados:', error);
    return res.status(500).json({ error: 'Erro ao processar a requisição' });
  }
};

// Endpoint para ranking de procuradores por data de depósito
export const rankingProcuradores = async (req: Request, res: Response): Promise<any> => {
  try { 
    const { ano, dataInicio, dataFim, excluirSemProcurador } = req.query;
    const deveExcluirSemProcurador = String(excluirSemProcurador).toLowerCase() === 'true';

    let dataFilter: any = {};
    
    if (ano) {
      const anoNum = Number(ano);
      dataFilter = {
        gte: new Date(`${anoNum}-01-01T00:00:00.000Z`),
        lt: new Date(`${anoNum + 1}-01-01T00:00:00.000Z`)
      };
    } else if (dataInicio && dataFim) {
      dataFilter = {
        gte: new Date(dataInicio as string),
        lte: new Date(dataFim as string)
      };
    } else {
      return res.status(400).json({
        error: 'É necessário fornecer o ano ou o período (dataInicio e dataFim)'
      });
    }

    const whereClauseProcesso: any = {
      dataDeposito: dataFilter,
    };

    if (deveExcluirSemProcurador) {
      whereClauseProcesso.procuradorId = { not: null };
    }

    const rankingAgrupado = await prisma.processo.groupBy({
      by: ['procuradorId'],
      where: whereClauseProcesso,
      _count: {
        _all: true,
      },
      orderBy: {
        _count: {
          id: 'desc'
        }
      }
    });

    const procuradorIds = rankingAgrupado
      .map(item => item.procuradorId)
      .filter(id => id !== null) as string[];

    let procuradorMap = new Map<string, string>();
    if (procuradorIds.length > 0) {
        const procuradores = await prisma.procurador.findMany({
          where: {
            id: { in: procuradorIds }
          },
          select: {
            id: true,
            nome: true
          }
        });
        procuradorMap = new Map(procuradores.map(p => [p.id, p.nome]));
    }

    const totalProcessosDepositados = await prisma.processo.count({
      where: whereClauseProcesso,
    });

    const resultado = rankingAgrupado
      .filter(item => !deveExcluirSemProcurador || item.procuradorId !== null)
      .map(item => {
        const nomeProcurador = procuradorMap.get(item.procuradorId as string) || (item.procuradorId === null ? 'Sem procurador' : 'Procurador não encontrado');
        const quantidadeProcessos = item._count._all;
        const porcentagem = totalProcessosDepositados > 0
          ? Number(((quantidadeProcessos / totalProcessosDepositados) * 100).toFixed(2))
          : 0;
        
        return {
          procuradorNome: nomeProcurador,
          quantidadeProcessos: quantidadeProcessos,
          porcentagem: porcentagem
        };
      });

    return res.status(200).json({
      data: resultado,
      total: totalProcessosDepositados
    });
  } catch (error) {
    console.error('Erro ao gerar ranking de procuradores por depósito:', error);
    return res.status(500).json({ error: 'Erro ao processar a requisição' });
  }
}; 