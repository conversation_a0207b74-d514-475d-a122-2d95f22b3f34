{"success": true, "message": "OK", "data": [{"id": ********, "hash": "egcq60bner4s84cgog00sgckcok0wcw", "rdstation_reference": "E-mail não informado", "type_reference": 3, "reference": "E-mail não informado", "temperature": null, "probability": null, "account_id": 13955, "pipeline_id": 56897, "owner_id": 79212, "stage_id": 341596, "person_id": ********, "company_id": null, "lost_reason_id": null, "origin_id": 457297, "started_in_stage_id": 341590, "created_at": "2025-05-07 09:35:31", "title": "[35] GAENA", "description": null, "observation": null, "status": 0, "closed_at": null, "reason_close": null, "deleted": 0, "freezed": 0, "value": 3890, "order": 2326, "updated_at": "2025-06-11 14:04:45", "last_stage_updated_at": "2025-06-11 14:04:45", "value_mrr": null, "probably_closed_at": null, "last_contact_at": "2025-06-11 14:04:45", "stage_changed_at": "2025-06-11 14:04:43", "frozen_at": null, "lead_time": 35, "customFields": [{"id": 196382, "name": "Ramo de atuação", "hash": "5e2ab920101b625d8fcc7d657bacae4c", "type": 1, "belongs": 1, "value": "revenda de produto de bebe e pet", "raw_value": "revenda de produto de bebe e pet", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": null, "options": null, "selected_options": null}, {"id": 232453, "name": "Canal", "hash": "09ec28a498ad27023b130d8d1bac0230", "type": 1, "belongs": 1, "value": "WhatsApp ", "raw_value": "WhatsApp ", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": null, "options": null, "selected_options": null}, {"id": 549882, "name": "IdOriginal", "hash": "fe3516735e9a34b77b65a07a51255344", "type": 14, "belongs": 1, "value": "47446375", "raw_value": 47446375, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 594787, "name": "<PERSON><PERSON> entrada lead", "hash": "ccd495150cb952162f0b4342adc3a2a0", "type": 1, "belongs": 1, "value": "29/04/2025, 14:59:04", "raw_value": "29/04/2025, 14:59:04", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 204059, "name": "Lead Score", "hash": "2efae4f52ad645c8fbbd483f586796f8", "type": 1, "belongs": 1, "value": null, "raw_value": null, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": null, "options": null, "selected_options": null}, {"id": 613811, "name": "Dispositivo", "hash": "3cbe466620723d4a0f2174e1d98a9d25", "type": 1, "belongs": 1, "value": null, "raw_value": null, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 196895, "name": "Indicado por", "hash": "ff160b3013b1fb3818a9b250c689b339", "type": 1, "belongs": 1, "value": null, "raw_value": null, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 213657, "name": "Nome da marca ✍️", "hash": "40641d0630e125b85f1d91b2e469b2b4", "type": 1, "belongs": 1, "value": "GAENA", "raw_value": "GAENA", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 213595, "name": "Estágio do negócio", "hash": "b581f8cef83aaaa7332129dc2552dd3e", "type": 3, "belongs": 1, "value": "Pretendo começar em breve", "raw_value": "Pretendo começar em breve", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["Comecei recentemente", "<PERSON><PERSON> atuo há mais de 6 meses", "<PERSON><PERSON> atuo há mais de 2 anos", "Pretendo começar em breve", "Por enquanto é só um plano"], "selected_options": "Pretendo começar em breve", "values_with_trash": ["Pretendo começar em breve"]}, {"id": 544091, "name": "fase-do-planejamento", "hash": "383add8ec4fbeb309870653586e667cb", "type": 3, "belongs": 1, "value": null, "raw_value": null, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["<PERSON>ão tenho logotipo ainda, mas quero garantir a propriedade do nome.", "Logotipo está pronto e quero registrar antes de iniciar a divulgação.", "Confirmar se o nome está disponível, antes de criar o logotipo.", "Somente saber se está disponível.", "Se estiver disponível, quero registrar.", "Verificar disponibilidade."], "selected_options": null, "values_with_trash": null}, {"id": 547311, "name": "Motivo do registro", "hash": "f3d22c9aab2dbc4b38e70e11f764921a", "type": 1, "belongs": 1, "value": null, "raw_value": null, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": null, "options": null, "selected_options": null}, {"id": 547312, "name": "Grau de conhecimento", "hash": "99903be8bba5e5a2081698321061b451", "type": 1, "belongs": 1, "value": null, "raw_value": null, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": null, "options": null, "selected_options": null}, {"id": 554713, "name": "Em quanto tempo quer resolver", "hash": "325820413d0fc4f14d4d1daa84feebcd", "type": 1, "belongs": 1, "value": null, "raw_value": null, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 554712, "name": "Quanto já investiu na marca", "hash": "c10b17029a905dd53719e773936777a8", "type": 1, "belongs": 1, "value": null, "raw_value": null, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 240584, "name": "Tipo de negócio", "hash": "f91331fcd8a32e775b1c7de2a4632ff5", "type": 3, "belongs": 1, "value": null, "raw_value": null, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["Vestu<PERSON><PERSON>", "Fabricação de produtos", "Comércio de produtos de outras marcas", "Prestação de serviço", "Produtor de conteúdo ou produto digital", "Entidade social ou organização religiosa", "Outros", "Comércio de produtos de marca própria"], "selected_options": null, "values_with_trash": null}, {"id": 194463, "name": "Classes ✍️", "hash": "8b7244acfcc87d24fbeebb7286ab6423", "type": 6, "belongs": 1, "value": ["35"], "raw_value": "[\"35\"]", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45"], "selected_options": ["35"], "values_with_trash": ["35"]}, {"id": 369377, "name": "Possui CNPJ", "hash": "83342876471f8508dd364202916d3b24", "type": 3, "belongs": 1, "value": "<PERSON>m", "raw_value": "<PERSON>m", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["<PERSON>m", "<PERSON><PERSON>, mas já estamos providenciando", "Atuo como pessoa física", "Não, mas estamos providenciando.", "Sim."], "selected_options": "<PERSON>m", "values_with_trash": ["<PERSON>m"]}, {"id": 213683, "name": "porte", "hash": "3843ba7f86f1357c881fca480f1d0754", "type": 6, "belongs": 1, "value": ["Ainda não identificado"], "raw_value": "[\"Ainda não identificado\"]", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["Pessoa física", "MEI", "ME", "EPP", "<PERSON><PERSON><PERSON>", "Inova Simples", "CNPJ em criação", "Ainda não identificado", "Associação", "<PERSON><PERSON> sei responder.", "DEMAIS"], "selected_options": ["Ainda não identificado"], "values_with_trash": ["Ainda não identificado"]}, {"id": 194466, "name": "Tipo de marca ✍️", "hash": "9fac515f2eaffe31a354c86baed1cb9a", "type": 6, "belongs": 1, "value": ["<PERSON><PERSON>"], "raw_value": "[\"Mista\"]", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["Nominativa", "<PERSON><PERSON>", "Figurativa"], "selected_options": ["<PERSON><PERSON>"], "values_with_trash": ["<PERSON><PERSON>"]}, {"id": 244306, "name": "sua-marca-possui-site-ou-redes-sociais", "hash": "d411c026484e16660fed999b270549c3", "type": 1, "belongs": 1, "value": null, "raw_value": null, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": null, "options": null, "selected_options": null}, {"id": 203714, "name": "Site e/ou redes sociais", "hash": "8626a4baa93f97d3bbf189ce631b1368", "type": 1, "belongs": 1, "value": null, "raw_value": null, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 244308, "name": "Quantos colaboradores?", "hash": "b0b3877ee5bde2ebc511f25210f4698c", "type": 3, "belongs": 1, "value": null, "raw_value": null, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["1 a 2", "3 a 5", "6 a 10", "11 a 50", "+ de 50", "Mais de 50"], "selected_options": null, "values_with_trash": null}, {"id": 225833, "name": "Tipo de serviço", "hash": "f832a9f2ab8eca6ad2cf81cf15050ebb", "type": 6, "belongs": 1, "value": ["Registro de marca"], "raw_value": "[\"Registro de marca\"]", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["Registro de marca", "Renovação de registro", "Atualização de registro", "Recurso administrativo", "Manifestação à Oposição", "Contranotificação"], "selected_options": ["Registro de marca"], "values_with_trash": ["Registro de marca"]}, {"id": 225839, "name": "Viabilidade de registro ✍️", "hash": "fb4b71d1a007bcf589278560161ee30d", "type": 3, "belongs": 1, "value": "Viável ✅", "raw_value": "Viável ✅", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["Viável ✅", "Inviável 🚫", "Viável com restrições", "Viável em algumas classes", "<PERSON><PERSON><PERSON><PERSON>", "Viável no conjunto misto"], "selected_options": "Viável ✅", "values_with_trash": ["Viável ✅"]}, {"id": 474111, "name": "Estratégia de atendimento e diagnóstico ✍️", "hash": "ef2dfea143f848b044a09538a4a4a2a8", "type": 5, "belongs": 1, "value": null, "raw_value": null, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 228243, "name": "Como foi atendido? ✍️", "hash": "a5236860f6af80fc755fb8be5b159c7d", "type": 6, "belongs": 1, "value": ["WhatsApp"], "raw_value": "[\"WhatsApp\"]", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["Ligação Telefônica", "WhatsApp", "E-mail", "Video conferência", "Presencial", "PDF", "I<PERSON>ress<PERSON>", "Instagram", "Linkedin", "Outra rede social"], "selected_options": ["WhatsApp"], "values_with_trash": ["WhatsApp"]}, {"id": 213682, "name": "Negociação ✍️", "hash": "8fde08d168df09b1f7b785d15ee90568", "type": 3, "belongs": 1, "value": null, "raw_value": null, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["Valor normal", "Valor promocional"], "selected_options": null, "values_with_trash": null}, {"id": 512979, "name": "<PERSON><PERSON><PERSON><PERSON> de Crédito ✍️", "hash": "5e62d130552affa0e8e73b6f8387a554", "type": 3, "belongs": 1, "value": "Aprovado ✚ Pagamento de entrada a combinar, protocolo padrão.", "raw_value": "Aprovado ✚ Pagamento de entrada a combinar, protocolo padrão.", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["Recusado", "Arriscado - Necess<PERSON>rio confirmar pagamento da entrada para que possamos realizar o protocolo.", "<PERSON>ova<PERSON>, pagamento de entrada em até 7 dias, protocolo padrão.", "Aprovado ✚ Pagamento de entrada a combinar, protocolo padrão.", "Não é necessário realizar a análise de crédito."], "selected_options": "Aprovado ✚ Pagamento de entrada a combinar, protocolo padrão.", "values_with_trash": ["Aprovado ✚ Pagamento de entrada a combinar, protocolo padrão."]}, {"id": 213659, "name": "Proposta no Qwril ✍️", "hash": "33ff940ed3cc66bc790db9a913ce08f6", "type": 3, "belongs": 1, "value": null, "raw_value": null, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["<PERSON>m", "Não"], "selected_options": null, "values_with_trash": null}, {"id": 213684, "name": "<PERSON>", "hash": "236fb94f5d58a794692a3415ef35adf0", "type": 9, "belongs": 1, "value": null, "raw_value": null, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 226320, "name": "Especificações", "hash": "b6c2ad9120aaf13d3e8374bd3d43ae8d", "type": 5, "belongs": 1, "value": "35\nProvimento de mercado on-line para compradores e vendedores de produtos e serviços [marketplace]; \nComércio [através de qualquer meio] de utensílios e recipientes para a casa ou a cozinha; \nComércio [através de qualquer meio] de artigos de cama, mesa e banho;", "raw_value": "35\nProvimento de mercado on-line para compradores e vendedores de produtos e serviços [marketplace]; \nComércio [através de qualquer meio] de utensílios e recipientes para a casa ou a cozinha; \nComércio [através de qualquer meio] de artigos de cama, mesa e banho;", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 545920, "name": "Plano", "hash": "04462ea78bc1a4ecc2e76ac7129c5be9", "type": 3, "belongs": 1, "value": "<PERSON><PERSON> completo", "raw_value": "<PERSON><PERSON> completo", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["Pacote simples", "<PERSON><PERSON> completo", "Não se aplica", "Plano especial 🪄"], "selected_options": "<PERSON><PERSON> completo", "values_with_trash": ["<PERSON><PERSON> completo"]}, {"id": 464564, "name": "Protocolo", "hash": "ca9c59b7f0edc9abd6840c84ec16980b", "type": 6, "belongs": 1, "value": ["Padrão: protocolo depois do faturamento"], "raw_value": "[\"Padrão: protocolo depois do faturamento\"]", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["Deluxe: protocolo autorizado", "Padrão: protocolo depois do faturamento", "24h: faturamento e protocolo em até 24h", "Arriscado: protocolo somente após a confirmação do recebimento"], "selected_options": ["Padrão: protocolo depois do faturamento"], "values_with_trash": ["Padrão: protocolo depois do faturamento"]}, {"id": 538627, "name": "Observações FATURAMENTO", "hash": "b6e1ef5dff432ef3beb1423efcfa202c", "type": 5, "belongs": 1, "value": "Ale, seguir normalmente", "raw_value": "Ale, seguir normalmente", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 464609, "name": "Forma de pagamento", "hash": "02735c2f9f01ea613f9d61a9650b7aa0", "type": 3, "belongs": 1, "value": "<PERSON><PERSON><PERSON>", "raw_value": "<PERSON><PERSON><PERSON>", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["À vista", "<PERSON><PERSON><PERSON>", "Cartão de Crédito", "Condição Especial ✴️"], "selected_options": "<PERSON><PERSON><PERSON>", "values_with_trash": ["<PERSON><PERSON><PERSON>"]}, {"id": 213681, "name": "Valor a cobrar", "hash": "452e46be16581bc793d3d5d40b4cf3b5", "type": 14, "belongs": 1, "value": "R$ 3.890,00", "raw_value": 3890, "formula": null, "output_type": null, "decimal_places": 2, "allow_negative": null, "currency_id": 1, "thousand_sep": 1, "options": null, "selected_options": null}, {"id": 464610, "name": "<PERSON><PERSON><PERSON><PERSON>", "hash": "f6c0401698a456ef172dc9215f1c8972", "type": 3, "belongs": 1, "value": "4 parcelas", "raw_value": "4 parcelas", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["1 parcela", "2 parcelas", "3 parcelas", "4 parcelas", "5 parcelas", "6 parcelas", "7 parcelas", "8 parcelas", "9 parcelas", "10 parcelas", "11 parcelas *", "12 parcelas *", "13 parcelas", "14 parcelas", "15 parcelas", "16 parcelas", "17 parcelas", "18 parcelas", "19 parcelas", "20 parcelas", "21 parcelas", "22 parcelas", "23 parcelas", "24 parcelas", "Sem parcelas"], "selected_options": "4 parcelas", "values_with_trash": ["4 parcelas"]}, {"id": 466497, "name": "Valor Entrada", "hash": "4fd1200ca0f2ecab18a39a67e0fd71ae", "type": 14, "belongs": 1, "value": "R$ 972,00", "raw_value": 972, "formula": null, "output_type": null, "decimal_places": 2, "allow_negative": null, "currency_id": 1, "thousand_sep": 1, "options": null, "selected_options": null}, {"id": 213680, "name": "Primeiro vencimento para", "hash": "5173151fd5b13f52cb57e6bfa508cbf6", "type": 10, "belongs": 1, "value": "02/05/2025", "raw_value": "2025-05-02", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 538628, "name": "Observações PROTOCOLO", "hash": "2b4be9376c646a7336c5c55db028e754", "type": 5, "belongs": 1, "value": "Seguir conforme orientações preenchidas. \nAntes do protocolo confirmar se o cliente não vai incluir o logo, ele ficou na dúvida.", "raw_value": "Seguir conforme orientações preenchidas. \nAntes do protocolo confirmar se o cliente não vai incluir o logo, ele ficou na dúvida.", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 661507, "name": "Observações sobre o paracer", "hash": "66587c5ebbd07d7b17409efe2caff782", "type": 5, "belongs": 1, "value": "<PERSON>ão possui anterioridades, nome fantasioso.", "raw_value": "<PERSON>ão possui anterioridades, nome fantasioso.", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 661502, "name": "<PERSON><PERSON> aux<PERSON> para a análise de viabilidade?", "hash": "8034c42baa166712abc332f29a1bf593", "type": 6, "belongs": 1, "value": ["Não"], "raw_value": "[\"Não\"]", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["Não", "<PERSON><PERSON><PERSON>", "Kadu", "<PERSON><PERSON><PERSON>"], "selected_options": ["Não"], "values_with_trash": ["Não"]}, {"id": 228249, "name": "Status do logotipo", "hash": "86ab312e990425e404ac32064dfecfa8", "type": 3, "belongs": 1, "value": "Cliente enviou, está na conversa", "raw_value": "Cliente enviou, está na conversa", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["Cliente enviou, está na conversa", "Cliente não enviou, solicitar", "Logo está em desenvolvimento para protocolo", "Nominativo 🚨 Atenção redobrada nos campos de nome da marca", "Outro ⚠️ Descrever no campo de observações"], "selected_options": "Cliente enviou, está na conversa", "values_with_trash": ["Cliente enviou, está na conversa"]}, {"id": 225853, "name": "Estado das especificações", "hash": "9ffcbfa84e2a3aa8337170de90ba91f6", "type": 3, "belongs": 1, "value": "Definidas", "raw_value": "Definidas", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["Definidas", "Validar com cliente", "Aprof<PERSON><PERSON>", "❇️ Livre preenchimento"], "selected_options": "Definidas", "values_with_trash": ["Definidas"]}, {"id": 225868, "name": "Titularidade / Faturamento", "hash": "5bd45571eadd6347eb7e07a66d213163", "type": 3, "belongs": 1, "value": "Pessoa Física", "raw_value": "Pessoa Física", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["Pessoa <PERSON>í<PERSON>", "Pessoa Física", "Multi-titularidade / Faturamento PJ", "Multi-titularidade / Faturamento CPF"], "selected_options": "Pessoa Física", "values_with_trash": ["Pessoa Física"]}, {"id": 664183, "name": "Condição de Pagamento Especial", "hash": "400c20279e00e288b2b1f6f72306d13a", "type": 5, "belongs": 1, "value": null, "raw_value": null, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 258486, "name": "Descrição para contrato de recurso administrativo. (Ver exemplo na descrição)", "hash": "420736e1032255b644a19be03ed75dbf", "type": 5, "belongs": 1, "value": null, "raw_value": null, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 679153, "name": "Tipo de contrato", "hash": "36258b10085b9dd9ce1c12ea12b27043", "type": 1, "belongs": 1, "value": "Completo - CPF - Parcelado [Registro]", "raw_value": "Completo - CPF - Parcelado [Registro]", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 679327, "name": "Link <PERSON>", "hash": "9760c2f315fa8405ea0a20faa429b7dd", "type": 9, "belongs": 1, "value": "https://app.clicksign.com/notarial/compat/requests/2270cd3e-ff0a-42a1-9f07-87097822dd2a", "raw_value": "https://app.clicksign.com/notarial/compat/requests/2270cd3e-ff0a-42a1-9f07-87097822dd2a", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 679292, "name": "<PERSON>", "hash": "51b4824e916e5c74b117507165be803b", "type": 5, "belongs": 1, "value": "https://signature.registrese.app.br/documents/47446375/Contrato_de_Assessoria_Gaena-assinado.pdf", "raw_value": "https://signature.registrese.app.br/documents/47446375/Contrato_de_Assessoria_Gaena-assinado.pdf", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 679956, "name": "Link Procuração Assinada", "hash": "3a91610a5f52128da59465aba306d8de", "type": 9, "belongs": 1, "value": "https://signature.registrese.app.br/documents/47446375/Procuracao_Gaena-assinado.pdf", "raw_value": "https://signature.registrese.app.br/documents/47446375/Procuracao_Gaena-assinado.pdf", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 693250, "name": "Anotações:", "hash": "b9f8f6a6c2502b6943f77c8e42281086", "type": 5, "belongs": 1, "value": null, "raw_value": null, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 194249, "name": "Número GRU", "hash": "fdbb4b72dafb35b5a370be9f94c85d05", "type": 1, "belongs": 1, "value": "29409172337380320", "raw_value": "29409172337380320", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 194250, "name": "Número Processo", "hash": "b11d4e52401560bfe06dfb3f4283e993", "type": 1, "belongs": 1, "value": "939538970", "raw_value": "939538970", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 587550, "name": "LinkProtocolo", "hash": "a1a4617305b87488d52e104f35291aaf", "type": 9, "belongs": 1, "value": "https://www.dropbox.com/scl/fo/1dzprzi6o0yusonie7ltq/ABXdXFiATcpKGnRZSRXkkDM?rlkey=e613c5cbqnancwx74y1wzx8vr&dl=0", "raw_value": "https://www.dropbox.com/scl/fo/1dzprzi6o0yusonie7ltq/ABXdXFiATcpKGnRZSRXkkDM?rlkey=e613c5cbqnancwx74y1wzx8vr&dl=0", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 587684, "name": "Data Depósito", "hash": "741932b9fbe261dbfeb73663657fd41f", "type": 10, "belongs": 1, "value": "11/06/2025", "raw_value": "2025-06-11", "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": null, "selected_options": null}, {"id": 577128, "name": "Movimentações", "hash": "af2052986dac0e51a3fa29edb39079f7", "type": 6, "belongs": 1, "value": null, "raw_value": null, "formula": null, "output_type": null, "decimal_places": 0, "allow_negative": null, "currency_id": null, "thousand_sep": 0, "options": ["Oposição", "Manifestação", "Exigência", "Desistência"], "selected_options": null, "values_with_trash": null}], "person": {"id": ********, "account_id": 13955, "company_id": null, "city_id": 4448, "owner_id": null, "manager_id": null, "cpf": "213.770.528-18", "avatar": null, "name": "<PERSON><PERSON><PERSON><PERSON>", "hash": "n9aag6ozbd9nwhbcmjasbbf0wrnv387", "website": null, "job_title": null, "gender": null, "birth_day": null, "observation": null, "customer_at": "2025-05-02", "facebook": null, "linkedin": null, "address_postal_code": "********", "address": "<PERSON><PERSON> <PERSON>", "address_number": null, "address_complement": null, "district": "Coral", "lat": null, "lng": null, "external_code": null, "status": true, "foreign_contact": 0, "data_legal_basis_processing": 1, "data_legal_origin_id": 3, "updated_at": "2025-06-11 14:04:04", "created_at": "2025-04-29 14:59:01", "deleted_at": null, "lgpd_declaration_accepted": false, "contactEmails": [{"id": ********, "account_id": 13955, "company_id": null, "person_id": ********, "type": "Outros", "email": "<EMAIL>", "is_main": 0, "email_nf": false}], "contactPhones": [{"id": ********, "account_id": 13955, "company_id": null, "person_id": ********, "type": "Outros", "phone": "*************", "is_main": 1}]}}], "meta": {"total": 1, "count": 1, "per_page": 15, "current_page": 1, "total_pages": 1, "links": []}}