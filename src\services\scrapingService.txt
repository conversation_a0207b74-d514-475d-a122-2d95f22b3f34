import { PrismaClient } from '@prisma/client';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';

const prisma = new PrismaClient();
const SCRAPING_SERVICE_URL = process.env.SCRAPING_SERVICE_URL || 'http://127.0.0.1:8000'; // Use variável de ambiente ou fallback

// Nome exato do procurador a ser filtrado
const NOME_PROCURADOR_FILTRO = "REGISTRE-SE LTDA";

/**
 * Identifica processos sem logomarca (para procuradores contendo o nome filtro OU processos monitorados)
 * e dispara uma tarefa de scraping no serviço Python.
 */
export async function iniciarScrapingLogomarcas(): Promise<void> {
    console.log(`[${new Date().toISOString()}] Iniciando verificação de logomarcas para processos monitorados ou com procurador contendo "${NOME_PROCURADOR_FILTRO}"...`);

    let jobId: string | null = null;
    let scrapingJobDbId: string | null = null;

    try {
        // 1. Buscar processos elegíveis diretamente
        // Condições: (Procurador contém NOME_PROCURADOR_FILTRO OU monitorado = true) E marca.temLogomarca = false
        const processosParaScrapear = await prisma.processo.findMany({
            where: {
                AND: [
                    {
                        OR: [
                            {
                                procurador: {
                                    nome: {
                                        contains: NOME_PROCURADOR_FILTRO,
                                        mode: 'insensitive' // Opcional: tornar a busca case-insensitive
                                    }
                                }
                            },
                            {
                                monitorado: true
                            }
                        ]
                    },
                    {
                        marca: {
                            temLogomarca: false
                        }
                    },
                    {
                        // Excluir processos de teste
                        NOT: {
                            numero: {
                                startsWith: 'TESTE-'
                            }
                        }
                    }
                ]
            },
            select: {
                numero: true // Seleciona apenas o número do processo
            }
        });

        const numerosProcesso = processosParaScrapear.map(p => p.numero);

        if (numerosProcesso.length === 0) {
            console.log(`[${new Date().toISOString()}] Nenhum processo elegível encontrado (monitorado ou procurador "${NOME_PROCURADOR_FILTRO}") sem logomarca.`);
            return;
        }

        console.log(`[${new Date().toISOString()}] ${numerosProcesso.length} processo(s) elegível(is) encontrado(s) para scraping de logomarca: ${numerosProcesso.join(', ')}`);

        // 3. Gerar Job ID e criar registro no banco
        jobId = uuidv4();
        const novoScrapingJob = await prisma.scrapingJob.create({
            data: {
                jobId: jobId,
                processoNumeros: numerosProcesso,
                status: 'PENDING' // Usando o valor do Enum definido no schema
            }
        });
        scrapingJobDbId = novoScrapingJob.id; // Guardar o ID do DB para updates

        console.log(`[${new Date().toISOString()}] ScrapingJob ${jobId} (DB ID: ${scrapingJobDbId}) criado com status PENDING.`);

        // 4. Montar payload e enviar para o serviço de scraping
        const payload = {
            processos: numerosProcesso,
            job_id: jobId
        };

        console.log(`[${new Date().toISOString()}] Enviando ${numerosProcesso.length} processo(s) para ${SCRAPING_SERVICE_URL}/scrape com job_id ${jobId}...`);

        const response = await axios.post(`${SCRAPING_SERVICE_URL}/scrape`, payload, {
            headers: { 'Content-Type': 'application/json' }
        });

        // 5. Atualizar status para SENT se sucesso
        if (response.status === 200 || response.status === 202) { // FastAPI pode retornar 202 Accepted para tarefas assíncronas
            await prisma.scrapingJob.update({
                where: { id: scrapingJobDbId },
                data: { status: 'SENT' }
            });
            console.log(`[${new Date().toISOString()}] ScrapingJob ${jobId} enviado com sucesso. Status atualizado para SENT.`);
        } else {
            // Considerar outros status 2xx como sucesso, mas logar um aviso?
            console.warn(`[${new Date().toISOString()}] Serviço de scraping retornou status inesperado ${response.status} para job ${jobId}.`);
            // Decidir se atualiza para SENT ou FAILED baseado na resposta. Vamos assumir FAILED por segurança.
            await prisma.scrapingJob.update({
                where: { id: scrapingJobDbId },
                data: {
                    status: 'FAILED',
                    errorMessage: `Status de resposta inesperado: ${response.status} ${response.statusText}`
                }
            });
        }

    } catch (error: any) {
        console.error(`[${new Date().toISOString()}] Erro ao iniciar scraping de logomarcas:`, error);

        // 6. Atualizar status para FAILED em caso de erro
        if (scrapingJobDbId) {
            try {
                await prisma.scrapingJob.update({
                    where: { id: scrapingJobDbId },
                    data: {
                        status: 'FAILED',
                        errorMessage: error.message || 'Erro desconhecido durante o processo.'
                    }
                });
                console.log(`[${new Date().toISOString()}] Status do ScrapingJob ${jobId} (DB ID: ${scrapingJobDbId}) atualizado para FAILED.`);
            } catch (updateError) {
                console.error(`[${new Date().toISOString()}] Falha ao atualizar status do ScrapingJob ${jobId} para FAILED:`, updateError);
            }
        } else {
            // Se o erro ocorreu antes de criar o job no DB
             console.error(`[${new Date().toISOString()}] Erro ocorreu antes da criação do registro ScrapingJob no banco de dados.`);
             // Poderíamos criar um registro WebhookPayloadFalho aqui se quiséssemos rastrear a falha de criação do job?
             // Por ora, apenas logamos.
        }

        // Se o erro foi na chamada Axios, podemos logar mais detalhes
        if (axios.isAxiosError(error)) {
            if (error.response) {
                // A requisição foi feita e o servidor respondeu com um status code fora do range 2xx
                console.error(`[${new Date().toISOString()}] Erro Axios - Resposta:`, error.response.data);
                console.error(`[${new Date().toISOString()}] Erro Axios - Status:`, error.response.status);
                console.error(`[${new Date().toISOString()}] Erro Axios - Headers:`, error.response.headers);
            } else if (error.request) {
                // A requisição foi feita mas nenhuma resposta foi recebida
                 console.error(`[${new Date().toISOString()}] Erro Axios - Nenhuma resposta recebida:`, error.request);
            } else {
                // Algo aconteceu ao configurar a requisição que acionou um erro
                console.error('[${new Date().toISOString()}] Erro Axios - Configuração:', error.message);
            }
        }
    } finally {
        // Opcional: Desconectar o Prisma se este script rodar isoladamente
         await prisma.$disconnect();
        // Se for parte de uma aplicação maior, geralmente não desconectamos aqui.
        console.log(`[${new Date().toISOString()}] Verificação de logomarcas concluída.`);
    }
}

// Exemplo de como chamar (para teste, remover depois):
iniciarScrapingLogomarcas().catch(console.error); 