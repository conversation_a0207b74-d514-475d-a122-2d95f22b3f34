import axios from "axios";
import dotenv from "dotenv";
dotenv.config();
interface FieldMapping {
  id: number;
  name: string;
}
export const fieldMapping: Record<string, FieldMapping> = {
  comoNosConheceu: { id: 627361, name: "Como nos conheceu" }, // Como nos conheceu
  idc: { id: 627129, name: "IDC" }, // IDC
  consentimento: { id: 623519, name: "Consentimento" }, // Consentimento
  nomeDaMarca: { id: 622328, name: "Nome da marca" }, // Nome da marca
  contratoAssinado: { id: 620401, name: "Contrato assinado em" }, // Contrato assinado em
  dispositivo: { id: 613811, name: "Dispositivo" }, // Dispositivo
  nacionalidade: { id: 599652, name: "Nacionalidade" }, // Nacionalidade
  horaEntradaLead: { id: 594787, name: "Hora entrada lead" }, // Hora entrada lead
  dataHoraConversao: { id: 594786, name: "Data e hora conversão" }, // Data e hora conversão
  dataHoraVisita: { id: 594785, name: "Data e hora visita" }, // Data e hora visita
  dataDeposito: { id: 587684, name: "Data Depósito" }, // Data Depósito
  linkProtocolo: { id: 587550, name: "LinkProtocolo" }, // LinkProtocolo
  variosTitulares: { id: 585212, name: "Vários titulares" }, // Vários titulares
  movimentacoes: { id: 577128, name: "Movimentações" }, // Movimentações
  equipamento: { id: 557294, name: "Equipamento" }, // Equipamento
  step: { id: 557284, name: "Step" }, // Step
  consultor: { id: 557283, name: "Consultor(a)" }, // Consultor(a)
  dataInicio: { id: 557278, name: "Data Início" }, // Data Início
  salarioBaseInicial: { id: 557277, name: "Salário Base Inicial" }, // Salário Base Inicial
  emQuantoTempoResolver: { id: 554713, name: "Em quanto tempo quer resolver" }, // Em quanto tempo quer resolver
  quantoInvestiuMarca: { id: 554712, name: "Quanto já investiu na marca" }, // Quanto já investiu na marca
  idOriginal: { id: 549882, name: "IdOriginal" }, // IdOriginal
  cadastradoPor: { id: 547334, name: "Cadastrado por" }, // Cadastrado por
  interacao: { id: 547315, name: "Interação" }, // Interação
  grauConhecimento: { id: 547312, name: "Grau de conhecimento" }, // Grau de conhecimento
  motivoRegistro: { id: 547311, name: "Motivo do registro" }, // Motivo do registro
  plano: { id: 545920, name: "Plano" }, // Plano
  fasePlanejamento: { id: 544091, name: "fase-do-planejamento" }, // fase-do-planejamento
  observacoesProtocolo: { id: 538628, name: "Observações PROTOCOLO" }, // Observações PROTOCOLO
  observacoesFaturamento: { id: 538627, name: "Observações FATURAMENTO" }, // Observações FATURAMENTO
  pTime: { id: 525692, name: "PTime" }, // PTime
  score: { id: 521462, name: "score" }, // score
  rangeScore: { id: 516253, name: "Range Score" }, // Range Score
  analiseCredito: { id: 512979, name: "Análise de Crédito ✍️" }, // Análise de Crédito ✍️
  tipoLead: { id: 495556, name: "tipo_lead" }, // tipo_lead
  urlConversao: { id: 480567, name: "url_conversao" }, // url_conversao
  estrategiaAtendimento: {
    id: 474111,
    name: "Estratégia de atendimento e diagnóstico ✍️",
  }, // Estratégia de atendimento e diagnóstico ✍️
  valorParcelas: { id: 466498, name: "Valor Parcelas" }, // Valor Parcelas
  valorEntrada: { id: 466497, name: "Valor Entrada" }, // Valor Entrada
  segundoVencimento: { id: 464611, name: "Segundo vencimento para" }, // Segundo vencimento para
  parcelas: { id: 464610, name: "Parcelas" }, // Parcelas
  formaPagamento: { id: 464609, name: "Forma de pagamento" }, // Forma de pagamento
  protocolo: { id: 464564, name: "Protocolo" }, // Protocolo
  tokenSite: { id: 442905, name: "token_site" }, // token_site
  perfil: { id: 424141, name: "Perfil" }, // Perfil
  observacoesCS: { id: 424140, name: "Observações CS" }, // Observações CS
  dia4: { id: 402547, name: "Dia 4" }, // Dia 4
  dia6: { id: 402546, name: "Dia 6" }, // Dia 6
  dia5: { id: 402545, name: "Dia 5" }, // Dia 5
  dia3: { id: 402543, name: "Dia 3" }, // Dia 3
  dia2: { id: 402542, name: "Dia 2" }, // Dia 2
  dia1: { id: 402541, name: "Dia 1" }, // Dia 1
  origemCampanha: { id: 389520, name: "Origem campanha" }, // Origem campanha
  keyword: { id: 388867, name: "Keyword" }, // Keyword
  conteudo: { id: 388866, name: "Conteúdo" }, // Conteúdo
  tipo: { id: 388865, name: "Tipo" }, // Tipo
  origem: { id: 388864, name: "Origem" }, // Origem
  campanha: { id: 388863, name: "Campanha" }, // Campanha
  pipeCanal: { id: 370155, name: "pipecanal" }, // pipecanal
  statusNegocio: { id: 369989, name: "Status/Situação do negócio" }, // Status/Situação do negócio
  possuiCNPJ: { id: 369377, name: "Possui CNPJ" }, // Possui CNPJ
  plataforma: { id: 369009, name: "Plataforma" }, // Plataforma
  descricaoContrato: {
    id: 258486,
    name: "Descrição para contrato de recurso administrativo. (Ver exemplo na descrição)",
  }, // Descrição para contrato de recurso administrativo. (Ver exemplo na descrição)
  pipeHash: { id: 256204, name: "pipehash" }, // pipehash
  redirecionarPara: { id: 256079, name: "redirecionar_para" }, // redirecionar_para
  token: { id: 256078, name: "token" }, // token
  entrada: { id: 246096, name: "entrada" }, // entrada
  quantosColaboradores: { id: 244308, name: "quantos-colaboradores" }, // quantos-colaboradores
  siteOuRedes: { id: 203714, name: "site-ou-redes" }, // site-ou-redes
  suaMarcaPossuiSiteOuRedes: {
    id: 244306,
    name: "sua-marca-possui-site-ou-redes-sociais",
  }, // sua-marca-possui-site-ou-redes-sociais
  company: { id: 244305, name: "company" }, // company
  qualDadoPrefereInformar: { id: 244304, name: "qualdadoprefereinformar" }, // qualdadoprefereinformar
  temCNPJ: { id: 369377, name: "tem-cnpj" }, // tem-cnpj
  temLogotipo: { id: 244302, name: "tem-logotipo" }, // tem-logotipo
  areaAtuacao: { id: 196382, name: "area-de-atuacao" }, // area-de-atuacao
  tipoNegocio: { id: 240584, name: "tipo-de-negocio" }, // tipo-de-negocio
  estagioNegocio: { id: 213595, name: "qual-o-estagio-do-seu-negocio" }, // qual-o-estagio-do-seu-negocio
  email: { id: 244297, name: "qual-e-o-seu-e-mail" }, // qual-e-o-seu-e-mail
  mobilePhone: { id: 244296, name: "mobile_phone" }, // mobile_phone
  razaoSocial: { id: 240589, name: "Razao social" }, // Razao social
  cnpj: { id: 240588, name: "CNPJ" }, // CNPJ
  emailPrincipal: { id: 240587, name: "Email" }, // Email
  tipoNegocioOpcao: { id: 240584, name: "Tipo de negócio" }, // Tipo de negócio
  canal: { id: 232453, name: "Canal" }, // Canal
  linkGRU: { id: 231836, name: "Link GRU" }, // Link GRU
  linkProcuracao: { id: 231771, name: "Link Procuração" }, // Link Procuração
  comoPrefereReceberContatoPosVenda: {
    id: 230722,
    name: "Como prefere receber contatos/avisos no pós venda?",
  }, // Como prefere receber contatos/avisos no pós venda?
  comoPrefereReceberContato: { id: 230721, name: "Como foi atendido" }, // Como foi atendido
  nomeLead: { id: 229327, name: "Nome Lead" }, // Nome Lead
  statusLogotipo: { id: 228249, name: "Status do logotipo" }, // Status do logotipo
  comoFoiAtendido: { id: 228243, name: "Como foi atendido? ✍️" }, // Como foi atendido? ✍️
  especificacoes: { id: 226320, name: "Especificações" }, // Especificações
  titularidadeFaturamento: { id: 225868, name: "Titularidade / Faturamento" }, // Titularidade / Faturamento
  faturarPara: { id: 225867, name: "Faturar para" }, // Faturar para
  estadoEspecificacoes: { id: 225853, name: "Estado das especificações" }, // Estado das especificações
  viabilidadeRegistro: { id: 225839, name: "Viabilidade de registro ✍️" }, // Viabilidade de registro ✍️
  tipoServico: { id: 225833, name: "Tipo de serviço" }, // Tipo de serviço
  comoPrefereContato: { id: 225830, name: "Como prefere receber o contato?" }, // Como prefere receber o contato?
  linkChatGuru: { id: 225778, name: "Link ChatGuru" }, // Link ChatGuru
  nomeLeadOutro: { id: 225754, name: "Nome Lead" }, // Nome Lead
  nomeLeadAdicional: { id: 225661, name: "Nome Lead" }, // Nome Lead
  observacoes: { id: 213728, name: "Observações" }, // Observações
  linkQwirl: { id: 213684, name: "Link Qwirl" }, // Link Qwirl
  porte: { id: 213683, name: "Porte" }, // porte
  negociacao: { id: 213682, name: "Negociação ✍️" }, // Negociação ✍️
  valorCobrar: { id: 213681, name: "Valor a cobrar" }, // Valor a cobrar
  primeiroVencimento: { id: 213680, name: "Primeiro vencimento para" }, // Primeiro vencimento para
  formaPagamentoOld: { id: 213678, name: "Forma de pagamento [old]" }, // Forma de pagamento [old]
  enderecoCompleto: {
    id: 213677,
    name: "Endereço completo (Nome da rua, nº 000, Bairro, Cidade, UF)",
  }, // Endereço completo (Nome da rua, nº 000, Bairro, Cidade, UF)
  propostaQwirl: { id: 213659, name: "Proposta no Qwril ✍️" }, // Proposta no Qwril ✍️
  nomeMarca: { id: 213657, name: "Nome da marca ✍️" }, // Nome da marca ✍️
  estagioNegocioOutro: { id: 213595, name: "Estágio do negócio" }, // Estágio do negócio
  sabeProcesso: { id: 213591, name: "Sabe como funciona o processo?" }, // Sabe como funciona o processo?
  canalOrigem: { id: 213590, name: "Canal de origem" }, // Canal de origem
  multiclasses: { id: 213582, name: "Multiclasses" }, // Multiclasses
  especificacoesApresentadas: {
    id: 213577,
    name: "Especificações apresentadas",
  }, // Especificações apresentadas
  leadScore: { id: 204059, name: "Lead Score" }, // Lead Score
  siteRedesSociais: { id: 203714, name: "Site e/ou redes sociais" }, // Site e/ou redes sociais
  indicadoPor: { id: 196895, name: "Indicado por" }, // Indicado por
  ramoAtuacao: { id: 196382, name: "Ramo de atuação" }, // Ramo de atuação
  funcaoCargo: { id: 194469, name: "Função ou cargo ✍️" }, // Função ou cargo ✍️
  motivoRegistroOutro: { id: 194468, name: "Motivo para o registro" }, // Motivo para o registro
  tipoMarca: { id: 194466, name: "Tipo de marca ✍️" }, // Tipo de marca ✍️
  classes: { id: 194463, name: "Classes ✍️" }, // Classes ✍️
  numeroProcesso: { id: 194250, name: "Número Processo" }, // Número Processo
  numeroGRU: { id: 194249, name: "Número GRU" }, // Número GRU
  possuiLogotipo: { id: 193118, name: "Possui logotipo?" }, // Possui logotipo?
};
export async function getCRMConfiguration() {
  const CRM_API_URL = "https://api.pipe.run/v1/deals";
  const CRM_TOKEN = process.env.CRM_TOKEN;
  if (!CRM_TOKEN) {
    throw new Error("Token do CRM não configurado");
  }
  return { CRM_API_URL, CRM_TOKEN };
}
// Função para atualizar status da oportunidade no CRM
export const updateDealStatus = async (dealId: string, crmApiUrl: string, crmToken: string) => {
  try {
    console.log(`[CRM] Atualizando status da oportunidade ${dealId} para Ganha`);
    const updateUrl = `${crmApiUrl}/${dealId}`;
    console.log(`[CRM] URL da requisição: ${updateUrl}`);
    
    const response = await axios.put(
      updateUrl,
      { status: 1 }, // Status 1 = Ganha
      {
        headers: {
          accept: "application/json",
          "content-type": "application/json",
          token: crmToken,
        },
      }
    );

    console.log(`[CRM] Resposta da atualização:`, response.data);
    return response.data;
  } catch (error: any) {
    console.error(`[CRM] ERRO ao atualizar status da oportunidade ${dealId}:`, {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status
    });
    throw error;
  }
};
