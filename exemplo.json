{"id": ********, "title": "[1] AQUARISMO BRASIL ANTONIO CÂNDIDO", "created_at": "2025-02-06 15:58:31", "closed_at": null, "probably_closed_at": null, "last_contact": "2025-02-18 14:22:58", "description": null, "observation": null, "status": 0, "deleted": 0, "freezed": 0, "frozen_at": null, "value": "3990.00", "value_mrr": "0.00", "hash": "cy59pubr8w848cgcscgwo8sg80gc0sc", "order": 2063, "probability": null, "updated_at": "2025-04-16 18:01:57", "stage_changed_at": "2025-04-16 18:01:57", "lead_time": 69, "temperature": null, "lost_reason": {"id": null, "name": null, "description": null}, "company": {"id": ********, "ie": null, "name": "PARAISO DOS AQUARIOS E PET SHOP LTDA", "cnpj": "02.626.548/0001-77", "hash": "48lpzuhn6ni88cgc8kcg00ss8ogo8wk", "logo": null, "open_at": "1998-07-15", "country": null, "website": null, "district": "Jacana", "email_nf": null, "created_at": "2024-05-17T14:44:05.000000Z", "observation": null, "company_name": "PARAISO DOS AQUARIOS E PET SHOP LTDA", "address_number": "173", "address_street": "RUA JOSE BUONO", "address_complement": null, "address_postal_code": "02273120", "lat": -23.463937, "lng": -46.58585, "address": {"street": "RUA JOSE BUONO", "postal_code": "02273120", "number": "173", "complement": null, "district": "Jacana"}, "size": "EMPRESA DE PEQUENO PORTE", "cnae": "47.89-0-04", "facebook": null, "linkedin": null, "status_touch": null, "company_situation": null, "city": {"id": 3826, "name": "São Paulo", "uf": "SP"}, "segment": {"id": null, "name": null, "sector": null}, "user": {"id": null, "name": null, "email": null, "telephone": null, "cellphone": null}, "cnaes": [{"id": "365", "code": "45.30-7-03", "description": "Comércio a varejo de peças e acessórios novos para veículos automotores"}, {"id": "393", "code": "46.23-1-09", "description": "Comércio atacadista de alimentos para animais"}, {"id": "423", "code": "46.42-7-01", "description": "Comércio atacadista de artigos do vestuário e acessórios, exceto profissionais e de segurança"}, {"id": "432", "code": "46.46-0-01", "description": "Comércio atacadista de cosméticos e produtos de perfumaria"}, {"id": "436", "code": "46.49-4-01", "description": "Comércio atacadista de equipamentos elétricos de uso pessoal e doméstico"}, {"id": "437", "code": "46.49-4-02", "description": "Comércio atacadista de aparelhos eletrônicos de uso pessoal e doméstico"}, {"id": "446", "code": "46.49-4-99", "description": "Comércio atacadista de outros equipamentos e artigos de uso pessoal e doméstico não especificados anteriormente"}, {"id": "447", "code": "46.51-6-01", "description": "Comércio atacadista de equipamentos de informática"}, {"id": "449", "code": "46.52-4-00", "description": "Comércio atacadista de componentes eletrônicos e equipamentos de telefonia e comunicação"}, {"id": "450", "code": "46.61-3-00", "description": "Comércio atacadista de máquinas, aparelhos e equipamentos para uso agropecuário; partes e peças"}, {"id": "454", "code": "46.65-6-00", "description": "Comércio atacadista de máquinas e equipamentos para uso comercial; partes e peças"}, {"id": "456", "code": "46.69-9-99", "description": "Comércio atacadista de outras máquinas e equipamentos não especificados anteriormente; partes e peças"}, {"id": "473", "code": "46.86-9-02", "description": "Comércio atacadista de embalagens"}, {"id": "477", "code": "46.89-3-99", "description": "Comércio atacadista especializado em outros produtos intermediários não especificados anteriormente"}, {"id": "480", "code": "46.93-1-00", "description": "Comércio atacadista de mercadorias em geral, sem predominância de alimentos ou de insumos agropecuários"}, {"id": "491", "code": "47.23-7-00", "description": "Comércio varejista de bebidas"}, {"id": "493", "code": "47.29-6-01", "description": "Tabacaria"}, {"id": "501", "code": "47.44-0-01", "description": "Comércio varejista de ferragens e ferramentas"}, {"id": "508", "code": "47.51-2-01", "description": "Comércio varejista especializado de equipamentos e suprimentos de informática"}, {"id": "510", "code": "47.52-1-00", "description": "Comércio varejista especializado de equipamentos de telefonia e comunicação"}, {"id": "511", "code": "47.53-9-00", "description": "Comércio varejista especializado de eletrodomésticos e equipamentos de áudio e vídeo"}, {"id": "512", "code": "47.54-7-01", "description": "Comércio varejista de móveis"}, {"id": "514", "code": "47.54-7-03", "description": "Comércio varejista de artigos de iluminação"}, {"id": "516", "code": "47.55-5-02", "description": "Comercio varejista de artigos de armarinho"}, {"id": "517", "code": "47.55-5-03", "description": "Comercio varejista de artigos de cama, mesa e banho"}, {"id": "518", "code": "47.56-3-00", "description": "Comércio varejista especializado de instrumentos musicais e acessórios"}, {"id": "521", "code": "47.59-8-99", "description": "Comércio varejista de outros artigos de uso pessoal e doméstico não especificados anteriormente"}, {"id": "524", "code": "47.61-0-03", "description": "Comércio varejista de artigos de papelaria"}, {"id": "526", "code": "47.63-6-01", "description": "Comércio varejista de brinquedos e artigos recreativos"}, {"id": "527", "code": "47.63-6-02", "description": "Comércio varejista de artigos esportivos"}, {"id": "528", "code": "47.63-6-03", "description": "Comércio varejista de bicicletas e triciclos; peças e acessórios"}, {"id": "529", "code": "47.63-6-04", "description": "Comércio varejista de artigos de caça, pesca e camping"}, {"id": "530", "code": "47.63-6-05", "description": "Comércio varejista de embarcações e outros veículos recreativos; peças e acessórios"}, {"id": "535", "code": "47.72-5-00", "description": "Comércio varejista de cosméticos, produtos de perfumaria e de higiene pessoal"}, {"id": "536", "code": "47.73-3-00", "description": "Comércio varejista de artigos médicos e ortopédicos"}, {"id": "538", "code": "47.81-4-00", "description": "Comércio varejista de artigos do vestuário e acessórios"}, {"id": "539", "code": "47.82-2-01", "description": "Comércio varejista de calçados"}, {"id": "540", "code": "47.82-2-02", "description": "Comércio varejista de artigos de viagem"}, {"id": "542", "code": "47.83-1-02", "description": "Comércio varejista de artigos de relojoaria"}, {"id": "545", "code": "47.89-0-01", "description": "Comércio varejista de suvenires, bijuterias e artesanatos"}, {"id": "547", "code": "47.89-0-03", "description": "Comércio varejista de objetos de arte"}, {"id": "553", "code": "47.89-0-09", "description": "Comércio varejista de armas e munições"}, {"id": "554", "code": "47.89-0-99", "description": "Comércio varejista de outros produtos não especificados anteriormente"}, {"id": "644", "code": "63.19-4-00", "description": "Portais, provedores de conteúdo e outros serviços de informação na internet"}, {"id": "740", "code": "82.11-3-00", "description": "Serviços combinados de escritório e apoio administrativo"}, {"id": "753", "code": "82.99-7-99", "description": "Outras atividades de serviços prestados principalmente às empresas não especificadas anteriormente"}], "contact_emails": [{"id": 42096475, "address": "<EMAIL>"}], "contact_phones": [{"id": 52670863, "number": "551122470455", "is_main": 1}], "economic_groups": [], "fields": [{"id": 630900, "nome": "Rede social da empresa", "tipo": 1, "valores": null, "valor": null}], "forms": []}, "person": {"id": ********, "hash": "koqzhgu8vl0uadwh15raf2oazq2mjjs", "cpf": "284.648.608-54", "name": "MARCELO LEPIANE", "job_title": "49-<PERSON><PERSON><PERSON>-Administrador", "rdstation": null, "birth_day": null, "gender": null, "website": null, "facebook": null, "linkedin": null, "avatar": null, "observation": "Pessoa cadastrada via integração JSON.", "data_legal_basis_processing": 1, "data_legal_origin_id": null, "lat": null, "lng": null, "address": {"street": "<PERSON><PERSON>", "postal_code": "02273120", "number": null, "complement": null, "district": "Jaçanã"}, "lgpd_declaration_accepted": false, "city": {"id": 3826, "uf": "SP", "name": "São Paulo"}, "company": {"id": ********, "name": "PARAISO DOS AQUARIOS E PET SHOP LTDA", "cnae": "47.89-0-04", "cnaes": [{"id": "365", "code": "45.30-7-03", "description": "Comércio a varejo de peças e acessórios novos para veículos automotores"}, {"id": "393", "code": "46.23-1-09", "description": "Comércio atacadista de alimentos para animais"}, {"id": "423", "code": "46.42-7-01", "description": "Comércio atacadista de artigos do vestuário e acessórios, exceto profissionais e de segurança"}, {"id": "432", "code": "46.46-0-01", "description": "Comércio atacadista de cosméticos e produtos de perfumaria"}, {"id": "436", "code": "46.49-4-01", "description": "Comércio atacadista de equipamentos elétricos de uso pessoal e doméstico"}, {"id": "437", "code": "46.49-4-02", "description": "Comércio atacadista de aparelhos eletrônicos de uso pessoal e doméstico"}, {"id": "446", "code": "46.49-4-99", "description": "Comércio atacadista de outros equipamentos e artigos de uso pessoal e doméstico não especificados anteriormente"}, {"id": "447", "code": "46.51-6-01", "description": "Comércio atacadista de equipamentos de informática"}, {"id": "449", "code": "46.52-4-00", "description": "Comércio atacadista de componentes eletrônicos e equipamentos de telefonia e comunicação"}, {"id": "450", "code": "46.61-3-00", "description": "Comércio atacadista de máquinas, aparelhos e equipamentos para uso agropecuário; partes e peças"}, {"id": "454", "code": "46.65-6-00", "description": "Comércio atacadista de máquinas e equipamentos para uso comercial; partes e peças"}, {"id": "456", "code": "46.69-9-99", "description": "Comércio atacadista de outras máquinas e equipamentos não especificados anteriormente; partes e peças"}, {"id": "473", "code": "46.86-9-02", "description": "Comércio atacadista de embalagens"}, {"id": "477", "code": "46.89-3-99", "description": "Comércio atacadista especializado em outros produtos intermediários não especificados anteriormente"}, {"id": "480", "code": "46.93-1-00", "description": "Comércio atacadista de mercadorias em geral, sem predominância de alimentos ou de insumos agropecuários"}, {"id": "491", "code": "47.23-7-00", "description": "Comércio varejista de bebidas"}, {"id": "493", "code": "47.29-6-01", "description": "Tabacaria"}, {"id": "501", "code": "47.44-0-01", "description": "Comércio varejista de ferragens e ferramentas"}, {"id": "508", "code": "47.51-2-01", "description": "Comércio varejista especializado de equipamentos e suprimentos de informática"}, {"id": "510", "code": "47.52-1-00", "description": "Comércio varejista especializado de equipamentos de telefonia e comunicação"}, {"id": "511", "code": "47.53-9-00", "description": "Comércio varejista especializado de eletrodomésticos e equipamentos de áudio e vídeo"}, {"id": "512", "code": "47.54-7-01", "description": "Comércio varejista de móveis"}, {"id": "514", "code": "47.54-7-03", "description": "Comércio varejista de artigos de iluminação"}, {"id": "516", "code": "47.55-5-02", "description": "Comercio varejista de artigos de armarinho"}, {"id": "517", "code": "47.55-5-03", "description": "Comercio varejista de artigos de cama, mesa e banho"}, {"id": "518", "code": "47.56-3-00", "description": "Comércio varejista especializado de instrumentos musicais e acessórios"}, {"id": "521", "code": "47.59-8-99", "description": "Comércio varejista de outros artigos de uso pessoal e doméstico não especificados anteriormente"}, {"id": "524", "code": "47.61-0-03", "description": "Comércio varejista de artigos de papelaria"}, {"id": "526", "code": "47.63-6-01", "description": "Comércio varejista de brinquedos e artigos recreativos"}, {"id": "527", "code": "47.63-6-02", "description": "Comércio varejista de artigos esportivos"}, {"id": "528", "code": "47.63-6-03", "description": "Comércio varejista de bicicletas e triciclos; peças e acessórios"}, {"id": "529", "code": "47.63-6-04", "description": "Comércio varejista de artigos de caça, pesca e camping"}, {"id": "530", "code": "47.63-6-05", "description": "Comércio varejista de embarcações e outros veículos recreativos; peças e acessórios"}, {"id": "535", "code": "47.72-5-00", "description": "Comércio varejista de cosméticos, produtos de perfumaria e de higiene pessoal"}, {"id": "536", "code": "47.73-3-00", "description": "Comércio varejista de artigos médicos e ortopédicos"}, {"id": "538", "code": "47.81-4-00", "description": "Comércio varejista de artigos do vestuário e acessórios"}, {"id": "539", "code": "47.82-2-01", "description": "Comércio varejista de calçados"}, {"id": "540", "code": "47.82-2-02", "description": "Comércio varejista de artigos de viagem"}, {"id": "542", "code": "47.83-1-02", "description": "Comércio varejista de artigos de relojoaria"}, {"id": "545", "code": "47.89-0-01", "description": "Comércio varejista de suvenires, bijuterias e artesanatos"}, {"id": "547", "code": "47.89-0-03", "description": "Comércio varejista de objetos de arte"}, {"id": "553", "code": "47.89-0-09", "description": "Comércio varejista de armas e munições"}, {"id": "554", "code": "47.89-0-99", "description": "Comércio varejista de outros produtos não especificados anteriormente"}, {"id": "644", "code": "63.19-4-00", "description": "Portais, provedores de conteúdo e outros serviços de informação na internet"}, {"id": "740", "code": "82.11-3-00", "description": "Serviços combinados de escritório e apoio administrativo"}, {"id": "753", "code": "82.99-7-99", "description": "Outras atividades de serviços prestados principalmente às empresas não especificadas anteriormente"}], "fields": [{"id": 630900, "nome": "Rede social da empresa", "tipo": 1, "valores": null, "valor": null}]}, "contact_emails": [{"id": 34744194, "address": "<EMAIL>"}], "contact_phones": [{"id": 46599869, "number": "5511952974865", "is_main": 1}], "fields": [{"id": 213677, "nome": "Endereço completo (Nome da rua, nº 000, Complemento, Bairro, Cidade, UF)", "tipo": 1, "valores": null, "valor": "<PERSON><PERSON>, 173, <PERSON><PERSON><PERSON>, São Paulo, SP"}, {"id": 225661, "nome": "Nome Lead", "tipo": 1, "valores": null, "valor": null}, {"id": 599652, "nome": "Nacionalidade", "tipo": 1, "valores": null, "valor": null}, {"id": 630901, "nome": "Rede social da pessoa", "tipo": 1, "valores": null, "valor": null}, {"id": 678015, "nome": "Portfólio", "tipo": 1, "valores": null, "valor": null}, {"id": 658815, "nome": "Teste de vendas consultivas", "tipo": 3, "valores": "[\"<70\",\"70-79\",\"80-89\",\"90-99\",\"100\"]", "valor": null}, {"id": 658816, "nome": "Teste dissertativo de registro de marcas", "tipo": 3, "valores": "[\"<3\",\"3\",\"3,5\",\"4\",\"4,5\",\"5\"]", "valor": null}, {"id": 658817, "nome": "Comentários sobre os testes", "tipo": 5, "valores": null, "valor": null}, {"id": 194469, "nome": "Função ou cargo ✍️", "tipo": 6, "valores": "[\"Proprietário\",\"Sócio\",\"Diretor\",\"Marketing\",\"Designer\",\"Jurídico\",\"Consultor\",\"Administrativo\",\"Financeiro\",\"Outro\"]", "valor": "[\"<PERSON><PERSON><PERSON>\"]"}, {"id": 225778, "nome": "Link ChatGuru", "tipo": 9, "valores": null, "valor": "https://s16.chatguru.app/chats#66451334ab18da993fe057f5"}]}, "tags": [{"id": 347793, "name": "<PERSON>", "color": "info"}], "stage": {"id": 341596, "name": "Monitoramento"}, "pipeline": {"id": 56897, "name": "CS 📆 Monitoramento de processos"}, "origin": {"id": 457297, "name": "Google"}, "user": {"id": 71078, "name": "<PERSON>", "avatar": "oci://account/13955/user/71078/avatar/7z0uwqi6k6o8sgc8ggkkco4g4wo4sgk.png", "email": "<EMAIL>", "telephone": "************", "cellphone": "************"}, "city": {"id": 3826, "name": "São Paulo", "uf": "SP"}, "proposals": [], "activities": [{"id": ********, "type": 5, "title": "Preparar documentação, procuração e cadastro INPI", "status": 2, "comment": "Atividade <b>Preparar documentação, procuração e cadastro INPI</b> concluída <br><br>ok", "date_end": "2025-02-06 08:10:00", "date_start": "2025-02-06 08:00:00", "description": "Se<PERSON>ir instruções do playbook", "activity_type_id": null, "companies": [{"id": ********, "name": "PARAISO DOS AQUARIOS E PET SHOP LTDA", "fields": [{"id": 630900, "nome": "Rede social da empresa", "tipo": 1, "valores": null, "valor": null}]}], "persons": [{"id": ********, "name": "MARCELO LEPIANE", "fields": [{"id": 213677, "nome": "Endereço completo (Nome da rua, nº 000, Complemento, Bairro, Cidade, UF)", "tipo": 1, "valores": null, "valor": "<PERSON><PERSON>, 173, <PERSON><PERSON><PERSON>, São Paulo, SP"}, {"id": 225661, "nome": "Nome Lead", "tipo": 1, "valores": null, "valor": null}, {"id": 599652, "nome": "Nacionalidade", "tipo": 1, "valores": null, "valor": null}, {"id": 630901, "nome": "Rede social da pessoa", "tipo": 1, "valores": null, "valor": null}, {"id": 678015, "nome": "Portfólio", "tipo": 1, "valores": null, "valor": null}, {"id": 658815, "nome": "Teste de vendas consultivas", "tipo": 3, "valores": "[\"<70\",\"70-79\",\"80-89\",\"90-99\",\"100\"]", "valor": null}, {"id": 658816, "nome": "Teste dissertativo de registro de marcas", "tipo": 3, "valores": "[\"<3\",\"3\",\"3,5\",\"4\",\"4,5\",\"5\"]", "valor": null}, {"id": 658817, "nome": "Comentários sobre os testes", "tipo": 5, "valores": null, "valor": null}, {"id": 194469, "nome": "Função ou cargo ✍️", "tipo": 6, "valores": "[\"Proprietário\",\"Sócio\",\"Diretor\",\"Marketing\",\"Designer\",\"Jurídico\",\"Consultor\",\"Administrativo\",\"Financeiro\",\"Outro\"]", "valor": "[\"<PERSON><PERSON><PERSON>\"]"}, {"id": 225778, "nome": "Link ChatGuru", "tipo": 9, "valores": null, "valor": "https://s16.chatguru.app/chats#66451334ab18da993fe057f5"}]}], "user": {"id": 59369, "name": "Eduarda | Registre.se", "avatar": "oci://account/13955/user/59369/avatar/h86jwnl6zbc4wssckkcgo4kwwowssk4.png", "email": "<EMAIL>", "telephone": null, "cellphone": null}, "booking": {"user": null, "timezone": null, "requested_date": null, "requested_timezone": null, "requested_date_timezone": null}}], "files": [{"id": ********, "url": "oci://account/13955/deals/********/files/f6tmy650lgoo88w4w8o8kg0swg0k00o.png", "name": "Protocolo.png", "size": 95540, "hash": "aqqxz5x3giok4okogcgkowoo0wwk0wg", "format": "png", "created_at": "2025-04-16T21:01:58.000000Z", "description": null, "user": {"id": 52245, "name": "Kadu | Registre.se", "avatar": "oci://account/13955/user/52245/avatar/tlzobwxzj68cw0cc4so8okoc8kws8g4.png", "email": "<EMAIL>", "telephone": "*************", "cellphone": "*************"}}], "fields": [{"id": 679153, "nome": "Tipo de contrato", "tipo": 1, "valores": null, "valor": null}, {"id": 679327, "nome": "Link <PERSON>", "tipo": 9, "valores": null, "valor": null}, {"id": 679956, "nome": "Link Procuração Assinada", "tipo": 9, "valores": null, "valor": null}], "forms": [{"id": 15587, "name": "⚫️ Dados Processo INPI", "type": 1, "page_title": null, "page_content": null, "page_background_color": "#ececfc", "page_text_color": "#333333", "page_logo_url": null, "form_background_color": null, "form_text_color": null, "form_button_background_color": null, "form_button_text_color": null, "form_with_rounded_borders": 0, "form_show_icons": true, "fields": {"0": {"id": 194249, "nome": "Número GRU", "tipo": 1, "valores": null, "valor": "*****************"}, "1": {"id": 194250, "nome": "Número Processo", "tipo": 1, "valores": null, "valor": "938807870"}, "2": {"id": 194463, "nome": "Classes ✍️", "tipo": 6, "valores": "[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\",\"11\",\"12\",\"13\",\"14\",\"15\",\"16\",\"17\",\"18\",\"19\",\"20\",\"21\",\"22\",\"23\",\"24\",\"25\",\"26\",\"27\",\"28\",\"29\",\"30\",\"31\",\"32\",\"33\",\"34\",\"35\",\"36\",\"37\",\"38\",\"39\",\"40\",\"41\",\"42\",\"43\",\"44\",\"45\"]", "valor": "[\"1\"]"}, "3": {"id": 194466, "nome": "Tipo de marca ✍️", "tipo": 6, "valores": "[\"<PERSON>mina<PERSON>va\",\"<PERSON><PERSON>\",\"Figurativa\"]", "valor": "[\"Mista\"]"}, "4": {"id": 213657, "nome": "Nome da marca ✍️", "tipo": 1, "valores": "null", "valor": "AQUARISMO BRASIL ANTONIO CÂNDIDO"}, "5": {"id": 225853, "nome": "Estado das especificações", "tipo": 3, "valores": "[\"Definidas\",\"Validar com cliente\",\"Aprofundar\",\"❇️ Livre preenchimento\"]", "valor": "Definidas"}, "6": {"id": 225868, "nome": "Titularidade / Faturamento", "tipo": 3, "valores": "[\"Pessoa Jurídica\",\"Pessoa Física\",\"Multi-titularidade \\/ Faturamento PJ\",\"Multi-titularidade \\/ Faturamento CPF\"]", "valor": "Pessoa <PERSON>í<PERSON>"}, "7": {"id": 226320, "nome": "Especificações", "tipo": 5, "valores": null, "valor": "Classe 01:\nÁcidos *;\n<PERSON>l<PERSON>is;\nCarbonato de sódio;\nCarboneto de cálcio [carbeto de cálcio];\n<PERSON><PERSON><PERSON> ativado;\nCarvão para filtros;\nHúmus;\nMateriais cerâmicos em partículas para uso como meio de filtragem;\nMaterial para filtragem constituído por preparações químicas;\nMaterial para filtragem constituído por substâncias minerais;\nMaterial para filtragem constituído por substâncias vegetais;\nMaterial para filtragem em matérias plásticas não processadas;\nSais [fertilizantes];\nSais de cálcio;\nSubstratos para cultivo fora do solo [agricultura];\nTerras raras [lantanídeos];\nTurfa [fertilizante];"}, "8": {"id": 231771, "nome": "Link Procuração", "tipo": 9, "valores": null, "valor": null}, "10": {"id": 228249, "nome": "Status do logotipo", "tipo": 3, "valores": "[\"Cliente enviou, está na conversa\",\"Cliente não enviou, solicitar\",\"Logo está em desenvolvimento para protocolo\",\"Nominativo 🚨 Atenção redobrada nos campos de nome da marca\",\"Outro ⚠️ Descrever no campo de observações\"]", "valor": "Cliente enviou, está na conversa"}, "12": {"id": 225833, "nome": "Tipo de serviço", "tipo": 6, "valores": "[\"Registro de marca\",\"Renovação de registro\",\"Atualização de registro\",\"Recurso administrativo\",\"Manifestação à Oposição\",\"Contranotificação\"]", "valor": "[\"Registro de marca\"]"}, "13": {"id": 464564, "nome": "Protocolo", "tipo": 6, "valores": "[\"Deluxe: protocolo autorizado\",\"Padrão: protocolo depois do faturamento\",\"24h: faturamento e protocolo em até 24h\",\"Arriscado: protocolo somente após a confirmação do recebimento\"]", "valor": "[\"Padrão: protocolo depois do faturamento\"]"}, "14": {"id": 538628, "nome": "Observações PROTOCOLO", "tipo": 5, "valores": null, "valor": "O logotipo já está na conversa.\nEu irei validar as especificações e solicitar o logotipo, por favor, aguarde a minha confirmação. Vou enviar via ChatGuru.\n\nCotitularidade de 2 CNPJS\n\nDados do segundo titular:\nCNPJ: 12.394.081/0001-30\n<PERSON> Neto\nCPF 286.265.518-00\nAv Barão da Vitória 129 São Paulo SP CEP 02552-010\n<EMAIL>"}, "15": {"id": 545920, "nome": "Plano", "tipo": 3, "valores": "[\"Pacote simples\",\"Pacote completo\",\"Não se aplica\",\"Plano especial 🪄\"]", "valor": "<PERSON><PERSON> completo"}, "16": {"id": 587550, "nome": "LinkProtocolo", "tipo": 9, "valores": null, "valor": "https://www.dropbox.com/scl/fo/8b9hg7qtko7nhvpv1zz1m/AKzthxHwgKyyrdPhFNYZkMQ?rlkey=fu8zihsvm38taej1qw08eowqg&dl=0"}, "17": {"id": 577128, "nome": "Movimentações", "tipo": 6, "valores": "[\"Oposição\",\"Manifestação\",\"Exigência\",\"Desistência\"]", "valor": null}, "18": {"id": 587684, "nome": "Data Depósito", "tipo": 10, "valores": null, "valor": null}, "19": {"id": 258486, "nome": "Descrição para contrato de recurso administrativo. (Ver exemplo na descrição)", "tipo": 5, "valores": null, "valor": null}, "20": {"id": 693250, "nome": "Anotações:", "tipo": 5, "valores": null, "valor": null}}}, {"id": 18721, "name": "Cadastro CNPJ", "type": 1, "page_title": "Cadastro de Pessoa Jurídica", "page_content": null, "page_background_color": "#2ba4be", "page_text_color": "#ffffff", "page_logo_url": null, "form_background_color": null, "form_text_color": null, "form_button_background_color": null, "form_button_text_color": null, "form_with_rounded_borders": 0, "form_show_icons": true, "fields": [{"id": 213683, "nome": "porte", "tipo": 6, "valores": "[\"Pessoa física\",\"MEI\",\"ME\",\"EPP\",\"Demais\",\"Inova Simples\",\"CNPJ em criação\",\"Ainda não identificado\",\"Associação\",\"Não sei responder.\",\"DEMAIS\"]", "valor": "[\"EPP\"]"}]}, {"id": 31509, "name": "Observações antigas", "type": 1, "page_title": "<PERSON><PERSON><PERSON><PERSON> exemplo", "page_content": "Texto de exemplo...", "page_background_color": "#FFFFFF", "page_text_color": "#000000", "page_logo_url": null, "form_background_color": "#E7E7E7", "form_text_color": "#000000", "form_button_background_color": "#ed5565", "form_button_text_color": "#FFFFFF", "form_with_rounded_borders": 1, "form_show_icons": true, "fields": [{"id": 213728, "nome": "Observações", "tipo": 5, "valores": null, "valor": null}]}, {"id": 34497, "name": "Comunicados", "type": 1, "page_title": "<PERSON><PERSON><PERSON><PERSON> exemplo", "page_content": "Texto de exemplo...", "page_background_color": "#FFFFFF", "page_text_color": "#000000", "page_logo_url": null, "form_background_color": "#E7E7E7", "form_text_color": "#000000", "form_button_background_color": "#ed5565", "form_button_text_color": "#FFFFFF", "form_with_rounded_borders": 1, "form_show_icons": true, "fields": [{"id": 577128, "nome": "Movimentações", "tipo": 6, "valores": "[\"Oposição\",\"Manifestação\",\"Exigência\",\"Desistência\"]", "valor": null}]}], "action": {"create": "2025-04-16 18:01:59", "pipeline": "CS 🔒 Registros de marca", "stage": "✅ Protocolado", "trigger_type": "Uma oportunidade entrar na etapa selecionada", "user": "<PERSON>"}}