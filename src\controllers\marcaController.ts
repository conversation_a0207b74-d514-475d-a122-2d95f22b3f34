import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export const atualizarMarca = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { nome, apresentacao, natureza } = req.body;

    // Verificar se a marca existe
    const marcaExistente = await prisma.marca.findUnique({
      where: { id }
    });

    if (!marcaExistente) {
      return res.status(404).json({ 
        success: false, 
        message: 'Marca não encontrada' 
      });
    }

    // Atualizar a marca
    const marcaAtualizada = await prisma.marca.update({
      where: { id },
      data: {
        nome: nome !== undefined ? nome : marcaExistente.nome,
        apresentacao: apresentacao !== undefined ? apresentacao : marcaExistente.apresentacao,
        natureza: natureza !== undefined ? natureza : marcaExistente.natureza
      },
      include: {
        ncl: true,
        cfe: true
      }
    });

    return res.status(200).json({
      success: true,
      message: 'Marca atualizada com sucesso',
      data: marcaAtualizada
    });
  } catch (error: any) {
    console.error(`Erro ao atualizar marca: ${error.message}`);
    return res.status(500).json({
      success: false,
      message: 'Erro ao atualizar marca',
      error: error.message
    });
  }
};