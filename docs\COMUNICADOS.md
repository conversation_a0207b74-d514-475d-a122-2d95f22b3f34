# Sistema de Comunicados de Prazo de Mérito

O sistema de comunicados de prazo de mérito foi desenvolvido para notificar automaticamente os clientes sobre o tempo restante estimado para o exame de mérito de seus processos de marca no INPI.

## Funcionalidades

1. **Verificação Diária**: O sistema verifica diariamente todos os processos ativos com data de mérito estimada.
2. **Comunicação Baseada em Múltiplos de 30 Dias**: Envia comunicados apenas quando o prazo restante em dias é um múltiplo exato de 30 (convertidos em meses).
3. **Integração com ChatGuru**: Utiliza a plataforma ChatGuru para enviar mensagens personalizadas aos clientes via WhatsApp.
4. **Controle de Duplicidade**: Evita o envio de comunicados duplicados para o mesmo prazo em meses.
5. **Relatórios e Estatísticas**: Fornece informações detalhadas sobre comunicados enviados.

## Como Funciona

1. Todos os dias às 9h da manhã, o sistema verifica todos os processos ativos que possuem uma data de mérito estimada.
2. Para cada processo, o sistema calcula o número de dias restantes até a data estimada.
3. Se o número de dias for um múltiplo exato de 30 (ex: 30, 60, 90...), o sistema verifica se já foi enviado um comunicado para esse prazo em meses.
4. Se não houver comunicado anterior para esse prazo, o sistema:
   - Atualiza o campo personalizado no ChatGuru com o estágio do processo (ex: "faltam 3 meses")
   - Executa o diálogo correspondente no ChatGuru para enviar a mensagem ao cliente
   - Registra o comunicado no banco de dados

## Endpoints da API

### Comunicados
- `GET /api/comunicados/prazo-merito` - Lista todos os comunicados (com paginação)
- `GET /api/comunicados/prazo-merito/processo/:processoId` - Lista comunicados de um processo específico
- `GET /api/comunicados/prazo-merito/estatisticas` - Obtém estatísticas dos comunicados
- `GET /api/comunicados/prazo-merito/relatorio` - Gera relatório detalhado de comunicados
- `POST /api/comunicados/prazo-merito/verificar` - Aciona manualmente a verificação e envio de comunicados

## Scripts de Teste

O sistema inclui os seguintes scripts para teste e depuração:

1. **testarComunicadosPrazo.ts**: Testa o envio de comunicados, mostrando quais processos seriam notificados
2. **testarMapeamentoDialogos.ts**: Verifica a configuração de mapeamento entre prazos e diálogos do ChatGuru
3. **testarRelatorioComunicados.ts**: Testa a geração de relatórios de comunicados enviados

## Mapeamento de Diálogos

O sistema utiliza o mapeamento definido em `src/utils/chatguru.utils.ts` para relacionar cada prazo em meses com:
- ID de etapa no ChatGuru
- ID de diálogo no ChatGuru
- Texto do estágio do processo a ser atualizado

## Modelo de Dados

O sistema utiliza o modelo `ComunicadoPrazoMerito` para armazenar os comunicados enviados, com os seguintes campos:
- `id`: Identificador único
- `processoId`: ID do processo relacionado
- `crmId`: ID do cliente no sistema CRM
- `dataEnvio`: Data e hora do envio
- `prazoEmMeses`: Prazo em meses para o mérito (ex: 1, 2, 3...)
- `dialogId`: ID do diálogo executado no ChatGuru
- `success`: Indica se o envio foi bem-sucedido
- `errorMessage`: Mensagem de erro (caso o envio falhe)

## Executando Manualmente

Para testar ou acionar manualmente o sistema de comunicados, você pode:

1. Usar o endpoint `POST /api/comunicados/prazo-merito/verificar`
2. Executar o script `npm run testarComunicados`

## Monitoramento e Logs

O sistema registra informações detalhadas no console sobre:
- Início da verificação diária
- Número de processos verificados
- Comunicados enviados e eventuais falhas
- Detalhes sobre cada processo processado

Essas informações podem ser acessadas nos logs da aplicação. 