import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Interface para os dados do CRM (baseado no retorno de buscarDadosCrm)
 */
interface DadosCrmParaProcesso {
  id: number;
  customFields?: any[];
  person?: any;
  company?: any;
  [key: string]: any;
}

/**
 * Mapeamento dos IDs dos campos customizados do CRM
 * (baseado no fieldMapping do crmWebhookService)
 */
const fieldMapping = {
  numeroProcesso: { id: 194250 },
  linkProtocolo: { id: 587550 },
  nomeMarca: { id: 213657 },
  tipoMarca: { id: 194466 },
  estadoEspecificacoes: { id: 225853 },
  classes: { id: 194463 },
};

/**
 * Extrai valor de campo customizado dos dados do CRM
 */
function extrairCampoCustomizado(dadosCrm: DadosCrmParaProcesso, fieldId: number): any {
  if (!dadosCrm.customFields || !Array.isArray(dadosCrm.customFields)) {
    console.log(`⚠️ customFields não encontrado ou não é array nos dados do CRM`);
    return null;
  }

  const campo = dadosCrm.customFields.find((field: any) => field.id === fieldId);
  
  if (!campo) {
    console.log(`⚠️ Campo customizado com ID ${fieldId} não encontrado`);
    return null;
  }

  return campo.value;
}

/**
 * Analisa e extrai dados necessários para criação do processo
 * Por enquanto apenas faz logs para verificar se conseguimos acessar os dados
 */
export async function analisarDadosParaCriacaoProcesso(
  dadosCrm: DadosCrmParaProcesso,
  numeroProcesso: string
): Promise<void> {
  console.log(`🔍 Analisando dados do CRM para criação do processo ${numeroProcesso}`);
  
  // Log da estrutura completa dos dados (para debug)
  console.log('📋 Estrutura completa dos dados do CRM:');
  console.log('- ID do Deal:', dadosCrm.id);
  console.log('- Tem customFields?', !!dadosCrm.customFields);
  console.log('- Número de customFields:', dadosCrm.customFields?.length || 0);
  console.log('- Tem person?', !!dadosCrm.person);
  console.log('- Tem company?', !!dadosCrm.company);
  
  if (dadosCrm.customFields && Array.isArray(dadosCrm.customFields)) {
    console.log('📝 IDs dos campos customizados disponíveis:');
    dadosCrm.customFields.forEach((field: any, index: number) => {
      console.log(`  [${index}] ID: ${field.id}, Value: ${JSON.stringify(field.value)}`);
    });
  }

  // --- 1. Número do Processo ---
  console.log(`\n🔢 NÚMERO DO PROCESSO:`);
  console.log(`- Esperado: ${numeroProcesso}`);
  
  const numeroProcessoCrm = extrairCampoCustomizado(dadosCrm, fieldMapping.numeroProcesso.id);
  console.log(`- Encontrado no CRM (ID ${fieldMapping.numeroProcesso.id}):`, numeroProcessoCrm);
  console.log(`- Match:`, numeroProcessoCrm === numeroProcesso);

  // --- 2. Link do Protocolo ---
  console.log(`\n🔗 LINK DO PROTOCOLO:`);
  const linkProtocolo = extrairCampoCustomizado(dadosCrm, fieldMapping.linkProtocolo.id);
  console.log(`- Valor (ID ${fieldMapping.linkProtocolo.id}):`, linkProtocolo);
  console.log(`- Tipo:`, typeof linkProtocolo);

  // --- 3. Nome da Marca ---
  console.log(`\n🏷️ NOME DA MARCA:`);
  const nomeMarca = extrairCampoCustomizado(dadosCrm, fieldMapping.nomeMarca.id);
  console.log(`- Valor (ID ${fieldMapping.nomeMarca.id}):`, nomeMarca);
  console.log(`- Tipo:`, typeof nomeMarca);

  // --- 4. Apresentação da Marca (Tipo) ---
  console.log(`\n🎨 APRESENTAÇÃO DA MARCA:`);
  const apresentacaoMarcaRaw = extrairCampoCustomizado(dadosCrm, fieldMapping.tipoMarca.id);
  console.log(`- Valor bruto (ID ${fieldMapping.tipoMarca.id}):`, apresentacaoMarcaRaw);
  console.log(`- Tipo:`, typeof apresentacaoMarcaRaw);
  
  let apresentacaoMarca = apresentacaoMarcaRaw;
  if (typeof apresentacaoMarcaRaw === 'string' && apresentacaoMarcaRaw.startsWith('[') && apresentacaoMarcaRaw.endsWith(']')) {
    try {
      const parsed = JSON.parse(apresentacaoMarcaRaw);
      if (Array.isArray(parsed) && parsed.length > 0) {
        apresentacaoMarca = String(parsed[0]);
        console.log(`- Processado como array, primeiro item:`, apresentacaoMarca);
      }
    } catch (e) {
      console.log(`- Erro ao fazer parse do JSON:`, e);
      apresentacaoMarca = String(apresentacaoMarcaRaw);
    }
  } else {
    apresentacaoMarca = String(apresentacaoMarcaRaw || '');
  }
  console.log(`- Valor final:`, apresentacaoMarca);

  // --- 5. Estado das Especificações ---
  console.log(`\n📋 ESTADO DAS ESPECIFICAÇÕES:`);
  const estadoEspecificacoes = extrairCampoCustomizado(dadosCrm, fieldMapping.estadoEspecificacoes.id);
  console.log(`- Valor (ID ${fieldMapping.estadoEspecificacoes.id}):`, estadoEspecificacoes);
  console.log(`- Tipo:`, typeof estadoEspecificacoes);

  // --- 6. Classes NCL ---
  console.log(`\n🏷️ CLASSES NCL:`);
  const nclClassesRaw = extrairCampoCustomizado(dadosCrm, fieldMapping.classes.id);
  console.log(`- Valor bruto (ID ${fieldMapping.classes.id}):`, nclClassesRaw);
  console.log(`- Tipo:`, typeof nclClassesRaw);
  
  let nclCodes: string[] = [];
  if (nclClassesRaw) {
    let potentialArray: any[] = [];
    
    if (typeof nclClassesRaw === 'string') {
      console.log(`- É string, verificando se é JSON array...`);
      if (nclClassesRaw.startsWith('[') && nclClassesRaw.endsWith(']')) {
        try {
          const parsed = JSON.parse(nclClassesRaw);
          if (Array.isArray(parsed)) {
            potentialArray = parsed;
            console.log(`- Parseado como array:`, potentialArray);
          }
        } catch (e) {
          console.log(`- Erro no parse JSON, splitando por vírgulas:`, e);
          potentialArray = nclClassesRaw.split(/[,\s]+/).map(code => code.trim());
        }
      } else {
        console.log(`- Não é JSON, splitando por vírgulas/espaços`);
        potentialArray = nclClassesRaw.split(/[,\s]+/).map(code => code.trim());
      }
    } else if (Array.isArray(nclClassesRaw)) {
      console.log(`- Já é array:`, nclClassesRaw);
      potentialArray = nclClassesRaw;
    }
    
    nclCodes = potentialArray
      .map(item => String(item).trim())
      .filter(code => {
        const isValid = /^[0-9]{1,2}$/.test(code);
        if (!isValid && code) {
          console.log(`  ⚠️ Código NCL inválido ignorado: "${code}"`);
        }
        return isValid;
      });
    
    console.log(`- Códigos NCL válidos extraídos:`, nclCodes);
  } else {
    console.log(`- Nenhuma classe NCL encontrada`);
  }

  // --- Resumo Final ---
  console.log(`\n📊 RESUMO DOS DADOS EXTRAÍDOS:`);
  console.log(`- Número do Processo: "${numeroProcesso}"`);
  console.log(`- Link do Protocolo: "${linkProtocolo || 'N/A'}"`);
  console.log(`- Nome da Marca: "${nomeMarca || 'N/A'}"`);
  console.log(`- Apresentação da Marca: "${apresentacaoMarca}"`);
  console.log(`- Estado das Especificações: "${estadoEspecificacoes || 'N/A'}"`);
  console.log(`- Classes NCL (${nclCodes.length}): [${nclCodes.join(', ')}]`);

  // --- Validações ---
  const dadosCompletos = !!(numeroProcesso && nomeMarca);
  console.log(`\n✅ VALIDAÇÃO:`);
  console.log(`- Dados mínimos completos: ${dadosCompletos ? 'SIM' : 'NÃO'}`);
  console.log(`- Número do processo preenchido: ${!!numeroProcesso}`);
  console.log(`- Nome da marca preenchido: ${!!nomeMarca}`);
  
  if (!dadosCompletos) {
    console.log(`⚠️ ATENÇÃO: Dados insuficientes para criar processo`);
  } else {
    console.log(`🎉 Dados suficientes para prosseguir com criação do processo`);
  }

  console.log(`\n--- FIM DA ANÁLISE PARA ${numeroProcesso} ---\n`);
}

/**
 * Cria processo no banco de dados a partir dos dados do CRM
 */
export async function criarProcessoAPartirDoCrm(
  dadosCrm: DadosCrmParaProcesso,
  numeroProcesso: string,
  dadosTitulares?: any
): Promise<any> {
  console.log(`🚀 Criando processo ${numeroProcesso} no banco a partir dos dados do CRM...`);
  
  // Primeiro analisar os dados (para logs de debug)
  await analisarDadosParaCriacaoProcesso(dadosCrm, numeroProcesso);
  
  // 🆕 Usar dados dos titulares já extraídos (se disponíveis)
  if (dadosTitulares && dadosTitulares.length > 0) {
    console.log(`👥 Usando dados dos titulares já extraídos: ${dadosTitulares.length} titular(es)`);
    
    // 🧪 Log dos dados dos titulares para análise
    dadosTitulares.forEach((titular: any, index: number) => {
      console.log(`📋 Titular ${index + 1}:`);
      console.log(`   Nome: ${titular.nome || 'N/A'}`);
      console.log(`   Documento: ${titular.numeroDocumento || 'N/A'}`);
      console.log(`   País: ${titular.pais || 'N/A'}`);
    });
  } else {
    console.log(`ℹ️ Nenhum dado de titular disponível para este processo`);
  }

  // --- Extrair dados do CRM (replicando lógica exata do webhook) ---
  const linkProtocolo = String(extrairCampoCustomizado(dadosCrm, fieldMapping.linkProtocolo.id) || '');
  const nomeMarca = String(extrairCampoCustomizado(dadosCrm, fieldMapping.nomeMarca.id) || '');

  // Processamento de apresentacaoMarca (IGUAL ao webhook)
  let apresentacaoMarca = extrairCampoCustomizado(dadosCrm, fieldMapping.tipoMarca.id);
  if (typeof apresentacaoMarca === 'string' && apresentacaoMarca.startsWith('[') && apresentacaoMarca.endsWith(']')) {
    try {
      const parsed = JSON.parse(apresentacaoMarca);
      if (Array.isArray(parsed) && parsed.length > 0) {
        apresentacaoMarca = String(parsed[0]);
      }
    } catch (e) {
      apresentacaoMarca = String(apresentacaoMarca);
    }
  } else if (Array.isArray(apresentacaoMarca) && apresentacaoMarca.length > 0) {
    // Para dados do CRM que vêm como array diretamente
    apresentacaoMarca = String(apresentacaoMarca[0]);
  } else {
    apresentacaoMarca = String(apresentacaoMarca || '');
  }

  const estadoEspecificacoes = String(extrairCampoCustomizado(dadosCrm, fieldMapping.estadoEspecificacoes.id) || '');
  
  // Processamento de Classes NCL (IGUAL ao webhook)
  const nclClassesRaw = extrairCampoCustomizado(dadosCrm, fieldMapping.classes.id);
  let nclCodes: string[] = [];

  if (nclClassesRaw) {
    let potentialArray: any[] = [];
    
    if (typeof nclClassesRaw === 'string') {
      if (nclClassesRaw.startsWith('[') && nclClassesRaw.endsWith(']')) {
        try {
          const parsed = JSON.parse(nclClassesRaw);
          if (Array.isArray(parsed)) {
            potentialArray = parsed;
          }
        } catch (e) {
          potentialArray = nclClassesRaw.split(/[,\s]+/).map(code => code.trim());
        }
      } else {
        potentialArray = nclClassesRaw.split(/[,\s]+/).map(code => code.trim());
      }
    } else if (Array.isArray(nclClassesRaw)) {
      potentialArray = nclClassesRaw;
    }
    
    nclCodes = potentialArray.map(item => String(item).trim()).filter(code => /^[0-9]{1,2}$/.test(code));
  }

  // --- Validar dados mínimos ---
  if (!numeroProcesso || !nomeMarca) {
    throw new Error(`Dados insuficientes para criar processo. Número: ${!!numeroProcesso}, Nome da Marca: ${!!nomeMarca}`);
  }

  console.log(`📝 Criando processo com os dados:`);
  console.log(`- Número: ${numeroProcesso}`);
  console.log(`- Nome da Marca: ${nomeMarca}`);
  console.log(`- Apresentação: ${apresentacaoMarca}`);
  console.log(`- Link Protocolo: ${linkProtocolo}`);
  console.log(`- Classes NCL: [${nclCodes.join(', ')}]`);

  // --- Criar no banco de dados (transação) ---
  try {
    const resultado = await prisma.$transaction(async (tx) => {
      // 1. Criar o Processo
      console.log(`📋 Criando processo ${numeroProcesso}...`);
      const processo = await tx.processo.create({
        data: {
          numero: numeroProcesso,
          linkProtocolo: linkProtocolo || null,
          monitorado: true,
          dataDeposito: new Date(new Date().toLocaleString("en-US", { timeZone: "America/Sao_Paulo" })),
          // Conectar ao procurador
          procuradorId: "65b3c0c0-aa3b-4d89-85fb-2a144e538800"
        },
        select: { id: true, numero: true }
      });

      console.log(`✅ Processo criado: ID ${processo.id}`);

      // 2. Criar Controle de Scraping
      console.log(`🔄 Criando controle de scraping...`);
      const agora = new Date();
      await tx.processoScrapingControl.create({
        data: {
          processo: { connect: { id: processo.id } },
          processoNumero: numeroProcesso,
          stage: 'AGUARDANDO_PUBLICACAO', // ScrapingStage.AGUARDANDO_PUBLICACAO
          isActive: true,
          stageEnteredAt: agora,
          nextScrapeDueAt: null, // Scraping não é ativo nesta fase inicial
        }
      });

      console.log(`✅ Controle de scraping criado`);

      // 3. Criar a Marca
      console.log(`🏷️ Criando marca...`);
      const marca = await tx.marca.create({
        data: {
          processo: { connect: { id: processo.id } },
          nome: nomeMarca || null,
          apresentacao: apresentacaoMarca || null,
        },
        select: { id: true }
      });

      console.log(`✅ Marca criada: ID ${marca.id}`);

      // 4. Adicionar Classes NCL (IGUAL ao webhook - verifica se já existe)
      if (nclCodes.length > 0) {
        console.log(`🔢 Processando ${nclCodes.length} classe(s) NCL...`);
        
        for (const code of nclCodes) {
          const existingNcl = await tx.nCL.findFirst({ 
            where: { marcaId: marca.id, codigo: code } 
          });
          
          if (!existingNcl) {
            await tx.nCL.create({ 
              data: { 
                marca: { connect: { id: marca.id } }, 
                codigo: code, 
                estadoDasEspecificacoes: estadoEspecificacoes || null 
              } 
            });
            console.log(`✅ Classe NCL ${code} criada`);
          } else {
            console.log(`ℹ️ Classe NCL ${code} já existe, ignorando`);
          }
        }

        console.log(`✅ Classes NCL processadas: [${nclCodes.join(', ')}]`);
      } else {
        console.log(`⚠️ Nenhuma classe NCL para processar`);
      }

      // 5. Criar titulares no processo
      await criarTitularesNoProcesso(processo.id, dadosTitulares, tx);

      return processo;
    });

    console.log(`🎉 Processo ${numeroProcesso} criado com sucesso no banco de dados!`);
    console.log(`📊 Resultado: ID ${resultado.id}, Número ${resultado.numero}`);
    
    return resultado;

  } catch (error) {
    console.error(`❌ Erro ao criar processo ${numeroProcesso} no banco:`, error);
    throw error;
  }
}

/**
 * Cria titulares no banco de dados a partir dos dados extraídos
 */
export async function criarTitularesNoProcesso(
  processoId: string, 
  dadosTitulares: any[], 
  tx: any,
  clienteId?: number
): Promise<void> {
  if (!dadosTitulares || dadosTitulares.length === 0) {
    console.log(`ℹ️ Nenhum titular para criar no processo ${processoId}`);
    return;
  }

  console.log(`👥 Criando ${dadosTitulares.length} titular(es) no processo ${processoId}...`);
  if (clienteId) {
    console.log(`🔗 Conectando titulares ao cliente ${clienteId}`);
  }

  for (let i = 0; i < dadosTitulares.length; i++) {
    const titular = dadosTitulares[i];
    
    try {
      // Validar dados mínimos
      if (!titular.nome) {
        console.log(`⚠️ Titular ${i + 1} sem nome - pulando`);
        continue;
      }

      // Criar titular no banco
      const novoTitular = await tx.titular.create({
        data: {
          processoId: processoId,
          nomeRazaoSocial: titular.nome,
          numeroDocumento: titular.numeroDocumento || null,
          pais: titular.pais || null,
          uf: titular.uf || null,
          clienteId: clienteId || null, // Conectar ao cliente se fornecido
          // Campos adicionais que não estão no modelo mas podem ser úteis para logs
          // endereco, cidade, cep são salvos apenas nos logs por enquanto
        }
      });

      console.log(`✅ Titular ${i + 1} criado: ${titular.nome} (ID: ${novoTitular.id})`);
      if (clienteId) {
        console.log(`   🔗 Conectado ao cliente ${clienteId}`);
      }
      
      // Log dos dados completos para debug
      console.log(`   📄 Documento: ${titular.numeroDocumento || 'N/A'}`);
      console.log(`   🏠 Endereço: ${titular.endereco || 'N/A'}`);
      console.log(`   🏙️ Cidade: ${titular.cidade || 'N/A'}`);
      console.log(`   🗺️ Estado: ${titular.uf || 'N/A'}`);
      console.log(`   📮 CEP: ${titular.cep || 'N/A'}`);
      console.log(`   🌍 País: ${titular.pais || 'N/A'}`);

    } catch (error) {
      console.error(`❌ Erro ao criar titular ${i + 1} (${titular.nome}):`, error);
      // Não quebrar o fluxo, apenas logar o erro
    }
  }

  console.log(`🎉 Processo de criação de titulares concluído para processo ${processoId}`);
}

/**
 * Conecta titulares existentes de um processo ao cliente
 */
export async function conectarTitularesAoCliente(
  processoId: string,
  clienteId: number,
  tx: any
): Promise<void> {
  try {
    console.log(`🔗 Conectando titulares do processo ${processoId} ao cliente ${clienteId}...`);
    
    const resultado = await tx.titular.updateMany({
      where: {
        processoId: processoId,
        clienteId: null // Apenas titulares que ainda não estão conectados
      },
      data: {
        clienteId: clienteId
      }
    });
    
    console.log(`✅ ${resultado.count} titular(es) conectado(s) ao cliente ${clienteId}`);
    
  } catch (error) {
    console.error(`❌ Erro ao conectar titulares ao cliente:`, error);
    throw error;
  }
} 