import { PrismaClient } from '@prisma/client';
import axios from 'axios';
import crypto from 'crypto';
import querystring from 'querystring';
import dotenv from 'dotenv';

dotenv.config();

const prisma = new PrismaClient();

interface DadosCrm {
  id: number;
  title: string;
  status: string;
  person?: {
    id: number;
    name: string;
    cpf?: string;
    contactPhones?: Array<{
      id: number;
      phone: string;
      is_main: number;
    }>;
    contactEmails?: Array<{
      id: number;
      email: string;
      is_main: number;
    }>;
  };
  company?: {
    id: number;
    name: string;
    cnpj?: string;
    contactPhones?: Array<{
      id: number;
      phone: string;
      is_main: number;
    }>;
    contactEmails?: Array<{
      id: number;
      email: string;
      is_main: number;
    }>;
  };
  customFields?: Array<{
    id: number;
    name: string;
    value: any;
  }>;
}

/**
 * Extrai identificador único a partir de um telefone (FUNÇÃO TESTADA E APROVADA)
 * Cópia exata da função do script criarClientesCrmCompleto.ts
 */
function extrairIdentificadorTelefone(telefone: string): string {
  console.log(`🔄 EXTRAÇÃO - Input: "${telefone}"`);
  
  if (!telefone || telefone === "NoPhone") {
    console.log(`❌ EXTRAÇÃO - Telefone inválido/vazio, retornando 0000000000`);
    return "0000000000";
  }

  const numeroLimpo = telefone.replace(/\D/g, '');
  console.log(`🧹 EXTRAÇÃO - Número limpo: "${numeroLimpo}" (${numeroLimpo.length} dígitos)`);
  
  if (!numeroLimpo) {
    console.log(`❌ EXTRAÇÃO - Número limpo vazio, retornando 0000000000`);
    return "0000000000";
  }

  let numeroProcessado = numeroLimpo;

  // Lista de DDDs válidos no Brasil
  const dddsValidos = new Set([
    11, 12, 13, 14, 15, 16, 17, 18, 19, // SP
    21, 22, 24, // RJ/ES
    27, 28, // ES
    31, 32, 33, 34, 35, 37, 38, // MG
    41, 42, 43, 44, 45, 46, // PR
    47, 48, 49, // SC
    51, 53, 54, 55, // RS
    61, // DF/GO
    62, 64, // GO
    63, // TO
    65, 66, // MT
    67, // MS
    68, // AC
    69, // RO
    71, 73, 74, 75, 77, // BA
    79, // SE
    81, 87, // PE
    82, // AL
    83, // PB
    84, // RN
    85, 88, // CE
    86, 89, // PI
    91, 93, 94, // PA
    92, 97, // AM
    95, // RR
    96, // AP
    98, 99 // MA
  ]);

  // CORREÇÃO: Detectar duplo "55" (país + DDD)
  if (numeroProcessado.startsWith('5555') && numeroProcessado.length >= 12) {
    // Remove primeiro "55" (código do país), mantém segundo "55" (DDD)
    numeroProcessado = numeroProcessado.substring(2);
  } else if (numeroProcessado.startsWith('55') && numeroProcessado.length >= 12) {
    // Verifica se após remover 55, temos um número válido brasileiro
    const semPrefixo = numeroProcessado.substring(2);
    
    if (semPrefixo.length === 10 || semPrefixo.length === 11) {
      const possibleDDD = parseInt(semPrefixo.substring(0, 2));
      if (dddsValidos.has(possibleDDD)) {
        numeroProcessado = semPrefixo;
      }
    }
  }

  // Se ainda não temos pelo menos 10 dígitos, preenche com zeros à direita
  if (numeroProcessado.length < 10) {
    numeroProcessado = numeroProcessado.padEnd(10, '0');
  }

  // Extrai DDD (primeiros 2 dígitos) e últimos 8 dígitos
  const ddd = numeroProcessado.substring(0, 2);
  const ultimosOitoDigitos = numeroProcessado.slice(-8);
  const resultado = ddd + ultimosOitoDigitos;
  
  console.log(`✅ EXTRAÇÃO - Resultado final: "${telefone}" → "${resultado}"`);
  return resultado;
}

const crmToken = process.env.CRM_TOKEN || "";

// Configurações de criptografia (idênticas ao gerarLinksAutoLogin.ts)
const ALGORITHM = 'aes-256-cbc';
const JWT_SECRET = process.env.JWT_SECRET!;
console.log("JWT_SECRETTTTTTT CARREGADO", JWT_SECRET);
const KEY = crypto.createHash('sha256').update(JWT_SECRET).digest();
const BASE_URL = 'https://cliente.registre.se';

/**
 * Busca dados do protocolo no CRM Pipe
 */
async function buscarDadosCrm(numeroProcesso: string): Promise<DadosCrm | null> {
  try {
    console.log(`🔍 Buscando protocolo ${numeroProcesso} no CRM...`);
    
    const response = await axios.get(
      `https://api.pipe.run/v1/deals?token=${crmToken}&custom_fields[194250]=${numeroProcesso}&with=person.contactPhones,person.contactEmails,company.contactPhones,customFields`,
      {
        headers: {
          token: crmToken,
        },
      }
    );

    if (response.data && response.data.data && response.data.data.length > 0) {
      return response.data.data[0];
    }
    
    console.log(`❌ Nenhum deal encontrado para protocolo ${numeroProcesso}`);
    return null;
  } catch (error) {
    console.error('Erro ao buscar no CRM:', error);
    throw error;
  }
}

/**
 * Gera todos os identificadores possíveis a partir dos telefones do CRM
 */
function gerarIdentificadoresPossiveis(dadosCrm: DadosCrm): string[] {
  const identificadores: Set<string> = new Set();
  
  
  if (dadosCrm.person?.contactPhones) {
    console.log(`📱 Encontrados ${dadosCrm.person.contactPhones.length} telefones da pessoa`);
    for (const telefone of dadosCrm.person.contactPhones) {
      console.log(`📞 Processando telefone pessoa: ${telefone.phone}`);
      const id = extrairIdentificadorTelefone(telefone.phone);
      console.log(`🎯 Identificador gerado: ${telefone.phone} → ${id}`);
      if (id && id !== "0000000000") {
        identificadores.add(id);
        console.log(`✅ Identificador válido adicionado: ${id}`);
      } else {
        console.log(`❌ Identificador inválido: ${id}`);
      }
    }
  } else {
    console.log(`❌ Nenhum telefone encontrado na pessoa`);
  }
  
  // Telefones da empresa
  console.log(`🏢 Company existe:`, !!dadosCrm.company);
  console.log(`📱 Company contactPhones:`, dadosCrm.company?.contactPhones);
  
  if (dadosCrm.company?.contactPhones) {
    console.log(`📱 Encontrados ${dadosCrm.company.contactPhones.length} telefones da empresa`);
    for (const telefone of dadosCrm.company.contactPhones) {
      console.log(`📞 Processando telefone empresa: ${telefone.phone}`);
      const id = extrairIdentificadorTelefone(telefone.phone);
      console.log(`🎯 Identificador gerado: ${telefone.phone} → ${id}`);
      if (id && id !== "0000000000") {
        identificadores.add(id);
        console.log(`✅ Identificador válido adicionado: ${id}`);
      } else {
        console.log(`❌ Identificador inválido: ${id}`);
      }
    }
  } else {
    console.log(`❌ Nenhum telefone encontrado na empresa`);
  }
  
  console.log(`📊 Total de identificadores válidos: ${identificadores.size}`);
  console.log(`📊 Identificadores finais: [${Array.from(identificadores).join(', ')}]`);
  
  return Array.from(identificadores);
}

/**
 * Busca ou cria cliente com base nos dados do CRM
 */
async function criarOuAtualizarCliente(dadosCrm: DadosCrm): Promise<{ cliente: any, criado: boolean, atualizado: boolean }> {
  const identificadores = gerarIdentificadoresPossiveis(dadosCrm);

  if (identificadores.length === 0) {
    throw new Error("Nenhum telefone válido encontrado no CRM");
  }

  console.log(`📱 Identificadores gerados: ${identificadores.join(", ")}`);

  // Buscar cliente existente por IDENTIFICADOR primeiro (chave de negócio)
  let clienteExistente = null;
  let tipoEncontro = "";

  // PRIORIDADE 1: Buscar por identificador (mais confiável)
  for (const identificador of identificadores) {
    clienteExistente = await prisma.cliente.findFirst({
      where: { identificador },
    });
    if (clienteExistente) {
      tipoEncontro = `identificador ${identificador}`;
      console.log(
        `✅ Cliente encontrado por identificador: ${identificador} (Cliente ID: ${clienteExistente.id})`
      );
      break;
    }
  }

  // PRIORIDADE 2: Se não encontrou por identificador, buscar por crmId (fallback)
  if (!clienteExistente) {
    clienteExistente = await prisma.cliente.findFirst({
      where: { crmId: dadosCrm.id },
    });
    if (clienteExistente) {
      tipoEncontro = `CRM ID ${dadosCrm.id}`;
      console.log(
        `✅ Cliente encontrado por CRM ID: ${dadosCrm.id} (Cliente ID: ${clienteExistente.id})`
      );
    }
  }

  // Preparar dados do cliente
  const nomeCliente =
    dadosCrm.person?.name || dadosCrm.company?.name || "Cliente";

  // Priorizar telefone principal
  let telefonePrincipal = "";
  let identificadorPrincipal = identificadores[0];

  if (dadosCrm.person?.contactPhones) {
    const telPrincipal =
      dadosCrm.person.contactPhones.find((t: any) => t.is_main === 1) ||
      dadosCrm.person.contactPhones[0];
    if (telPrincipal) {
      telefonePrincipal = telPrincipal.phone;
      const idPrincipal = extrairIdentificadorTelefone(telPrincipal.phone);
      if (idPrincipal && idPrincipal !== "0000000000")
        identificadorPrincipal = idPrincipal;
    }
  } else if (dadosCrm.company?.contactPhones?.[0]) {
    telefonePrincipal = dadosCrm.company.contactPhones[0].phone;
    const idPrincipal = extrairIdentificadorTelefone(telefonePrincipal);
    if (idPrincipal && idPrincipal !== "0000000000")
      identificadorPrincipal = idPrincipal;
  }
  // Função para obter documento
  const obterDocumento = (oportunidade: DadosCrm): {
    documento?: string;
    tipo?: string;
  } => {
    if (oportunidade.person?.cpf) {
      return { documento: oportunidade.person.cpf, tipo: "CPF" };
    }

    if (oportunidade.company?.cnpj) {
      return { documento: oportunidade.company.cnpj, tipo: "CNPJ" };
    }

    return {};
  }
  // Preparar dados para atualização/criação
  const numeroDocumento = obterDocumento(dadosCrm);
  console.log("numeroDocumento", numeroDocumento);
  console.log("numeroDocumento.documento", numeroDocumento.documento);
  console.log("numeroDocumento.tipo", numeroDocumento.tipo);

  let dadosCliente: any = {
    nome: nomeCliente,
    crmId: dadosCrm.id,
    numeroDocumento: numeroDocumento.documento || undefined,
    tipoDeDocumento: numeroDocumento.tipo || undefined,
    camposPersonalizados: dadosCrm.customFields || undefined,
  };

  // DECISÃO INTELIGENTE SOBRE IDENTIFICADOR
  if (clienteExistente) {
    if (tipoEncontro.includes("identificador")) {
      // Encontrou por identificador: manter o identificador atual (pode atualizar outros dados)
      console.log(
        `🔄 Cliente encontrado por identificador: mantendo identificador existente: ${clienteExistente.identificador}`
      );
      dadosCliente.identificador = clienteExistente.identificador;
    } else if (tipoEncontro.includes("CRM ID")) {
      // Encontrou por crmId: PRESERVAR identificador existente se houver
      if (
        clienteExistente.identificador &&
        clienteExistente.identificador !== "0000000000"
      ) {
        console.log(
          `🛡️ Cliente encontrado por CRM ID: PRESERVANDO identificador existente: ${clienteExistente.identificador}`
        );
        dadosCliente.identificador = clienteExistente.identificador;
      } else {
        console.log(
          `📞 Cliente encontrado por CRM ID mas sem identificador válido: atualizando para: ${identificadorPrincipal}`
        );
        dadosCliente.identificador = identificadorPrincipal;
      }
    }
  } else {
    // Cliente novo: usar identificador gerado
    dadosCliente.identificador = identificadorPrincipal;
  }

  console.log(
    `🏷️ Campos personalizados: ${
      dadosCrm.customFields?.length || 0
    } campos inseridos como array`
  );
  console.log(`📱 Identificador final: ${dadosCliente.identificador}`);

  // Unificar lógica: tratar cliente existente igual a novo
  let cliente: any;
  let criado = false;
  let atualizado = false;

  if (clienteExistente) {
    console.log(
      `🔄 Atualizando cliente existente encontrado por ${tipoEncontro}`
    );

    cliente = await prisma.cliente.upsert({
      where: { id: clienteExistente.id },
      create: dadosCliente, // Não deveria ser executado já que cliente existe
      update: dadosCliente,
    });
    atualizado = true;
  } else {
    console.log(`➕ Criando novo cliente: ${nomeCliente}`);

    // VERIFICAÇÃO FINAL: Garantir que não existe cliente com este identificador
    const verificacaoFinal = await prisma.cliente.findFirst({
      where: { identificador: identificadorPrincipal },
    });

    if (verificacaoFinal) {
      console.log(
        `⚠️ ATENÇÃO: Cliente com identificador ${identificadorPrincipal} já existe (ID: ${verificacaoFinal.id}). Atualizando ao invés de criar.`
      );

      cliente = await prisma.cliente.update({
        where: { id: verificacaoFinal.id },
        data: dadosCliente,
      });
      atualizado = true;
    } else {
      cliente = await prisma.cliente.create({
        data: dadosCliente,
      });
      criado = true;
    }
  }

  // LÓGICA DE CONTATOS (igual ao script criarClientesCrmCompleto.ts)
  console.log(`📇 Criando contatos para cliente ID: ${cliente.id}`);

  // Remover contatos antigos para recriar
  await prisma.contatoCliente.deleteMany({
    where: { clienteId: cliente.id },
  });

  // Contato principal (person ou company)
  const emailPrincipal =
    dadosCrm.person?.contactEmails?.find((e: any) => e.is_main === 1)?.email ||
    dadosCrm.person?.contactEmails?.[0]?.email ||
    dadosCrm.company?.contactEmails?.[0]?.email;

  await prisma.contatoCliente.create({
    data: {
      clienteId: cliente.id,
      telefone: telefonePrincipal,
      email: emailPrincipal,
    },
  });
  console.log(
    `✅ Contato principal criado: ${telefonePrincipal} | ${
      emailPrincipal || "sem email"
    }`
  );

  // Contato adicional se company tem dados diferentes
  if (dadosCrm.person && dadosCrm.company) {
    const telefoneCompany = dadosCrm.company.contactPhones?.[0]?.phone;
    const emailCompany = dadosCrm.company.contactEmails?.[0]?.email;

    if (telefoneCompany && telefoneCompany !== telefonePrincipal) {
      await prisma.contatoCliente.create({
        data: {
          clienteId: cliente.id,
          telefoneSegundario: telefoneCompany,
          email: emailCompany,
        },
      });
      console.log(
        `➕ Contato adicional criado: telefoneSegundario=${telefoneCompany} | ${
          emailCompany || "sem email"
        }`
      );
    }
  }

  return { cliente, criado, atualizado };
}

/**
 * Conecta o cliente ao processo existente no banco
 */
async function conectarClienteAoProcesso(clienteId: number, numeroProcesso: string, dadosCrm?: any, dadosTitulares?: any): Promise<any> {
  // Buscar o processo que já deve existir (criado pelo webhook)
  let processo = await prisma.processo.findUnique({
    where: { numero: numeroProcesso },
    include: {
      titulares: true // Incluir titulares para verificar se já existem
    }
  });
  
  if (!processo) {
    console.log(`⚠️ Processo ${numeroProcesso} não encontrado no banco.`);
    
    if (dadosCrm) {
      console.log(`🔧 Tentando criar processo a partir dos dados do CRM...`);
      
      try {
        // Importar a função de criação de processo
        const { criarProcessoAPartirDoCrm } = await import('./criacaoProcessoService');
        
        // Criar o processo a partir dos dados do CRM e titulares
        processo = await criarProcessoAPartirDoCrm(dadosCrm, numeroProcesso, dadosTitulares);
        
        console.log(`✅ Processo ${numeroProcesso} criado com sucesso a partir dos dados do CRM!`);
        
      } catch (criacaoError) {
        console.error(`❌ Erro ao criar processo a partir dos dados do CRM:`, criacaoError);
        throw new Error(`Processo ${numeroProcesso} não encontrado no banco e falha na criação automática: ${criacaoError}`);
      }
      
    } else {
      throw new Error(`Processo ${numeroProcesso} não encontrado no banco. Webhook pode não ter sido processado e não há dados do CRM para criação automática.`);
    }
  } else {
    console.log(`✅ Processo ${numeroProcesso} encontrado no banco (ID: ${processo.id})`);
    
    // Verificar se o processo tem titulares
    if (!processo.titulares || processo.titulares.length === 0) {
      console.log(`⚠️ Processo existe mas não possui titulares - criando titulares...`);
      
      if (dadosTitulares && dadosTitulares.length > 0) {
        try {
          // Importar função para criar titulares
          const { criarTitularesNoProcesso } = await import('./criacaoProcessoService');
          
          // Criar titulares no processo existente e conectar ao cliente
          await prisma.$transaction(async (tx) => {
            await criarTitularesNoProcesso(processo!.id, dadosTitulares, tx, clienteId);
          });
          
          console.log(`✅ ${dadosTitulares.length} titular(es) criado(s) no processo existente ${numeroProcesso}`);
          
        } catch (titularError) {
          console.error(`❌ Erro ao criar titulares no processo existente:`, titularError);
          // Não quebrar o fluxo, apenas logar o erro
        }
      } else {
        console.log(`ℹ️ Nenhum dado de titular disponível para criar`);
      }
    } else {
      console.log(`👥 Processo já possui ${processo.titulares.length} titular(es):`);
      processo.titulares.forEach((titular, index) => {
        console.log(`   ${index + 1}. ${titular.nomeRazaoSocial} (${titular.numeroDocumento || 'sem documento'})`);
      });
      
      // Verificar se os titulares já estão conectados ao cliente
      const titularesSemCliente = processo.titulares.filter(t => !t.clienteId);
      if (titularesSemCliente.length > 0) {
        console.log(`🔗 Conectando ${titularesSemCliente.length} titular(es) ao cliente ${clienteId}...`);
        
        try {
          // Importar função para conectar titulares
          const { conectarTitularesAoCliente } = await import('./criacaoProcessoService');
          
          await prisma.$transaction(async (tx) => {
            await conectarTitularesAoCliente(processo!.id, clienteId, tx);
          });
          
        } catch (conexaoError) {
          console.error(`❌ Erro ao conectar titulares ao cliente:`, conexaoError);
          // Não quebrar o fluxo, apenas logar o erro
        }
      } else {
        console.log(`✅ Todos os titulares já estão conectados a clientes`);
      }
    }
  }
  
  // Verificar se processo foi encontrado/criado
  if (!processo) {
    throw new Error(`Erro crítico: Processo ${numeroProcesso} não pôde ser encontrado nem criado`);
  }
  
  // Se já está conectado ao cliente, retornar
  if (processo.clienteId === clienteId) {
    console.log(`🔗 Processo ${numeroProcesso} já conectado ao cliente ${clienteId}`);
    return processo;
  }
  
  // Conectar o cliente ao processo existente
  console.log(`🔗 Conectando cliente ${clienteId} ao processo ${numeroProcesso}`);
  
  const processoAtualizado = await prisma.processo.update({
    where: { id: processo.id },
    data: { clienteId }
  });
  
      return processoAtualizado;
}

/**
 * Gera código curto de 6 caracteres (igual ao script original)
 */
function generateShortCode(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 6; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * Gera código único (igual ao script original)
 */
async function generateUniqueShortCode(): Promise<string> {
  let shortCode: string;
  let attempts = 0;
  const maxAttempts = 10;
  
  do {
    shortCode = generateShortCode();
    attempts++;
    
    if (attempts >= maxAttempts) {
      throw new Error('Não foi possível gerar um código único após várias tentativas');
    }
  } while (!(await isShortCodeUnique(shortCode)));
  
  return shortCode;
}

/**
 * Verifica se código já existe
 */
async function isShortCodeUnique(shortCode: string): Promise<boolean> {
  const existing = await prisma.shortUrl.findUnique({
    where: { shortCode }
  });
  return !existing;
}

/**
 * Criptografa credenciais (CÓPIA EXATA do gerarLinksAutoLogin.ts)
 */
function encryptCredentials(identificador: string, numeroDocumento: string): string {
  try {
    // Pegar últimos 3 dígitos do documento como senha (somente números)
    const apenasNumeros = numeroDocumento.replace(/\D/g, ''); // Remove tudo que não é dígito
    const senha = apenasNumeros.slice(-3);
    
    // Criar payload das credenciais
    const payload = {
      identificador,
      senha,
      timestamp: Date.now()
    };
    
    console.log('🔍 Backend - Criptografando:', { identificador, senha: senha.substring(0, 1) + '**' });
    
    // Criptografar usando AES-256-CBC
    const iv = crypto.randomBytes(16); // 16 bytes = 128 bits
    const cipher = crypto.createCipheriv(ALGORITHM, KEY, iv);
    
    console.log('🔍 Backend - IV gerado (buffer):', iv);
    console.log('🔍 Backend - IV length (bytes):', iv.length);
    
    // CRÍTICO: IV deve ser convertido para HEX (não base64)
    const ivHex = iv.toString('hex');
    console.log('🔍 Backend - IV em HEX:', ivHex);
    console.log('🔍 Backend - IV hex length:', ivHex.length); // Deve ser 32
    
    if (ivHex.length !== 32) {
      throw new Error(`IV hex tem tamanho inválido: ${ivHex.length}, esperado: 32`);
    }
    
    // CRÍTICO: Dados criptografados também em HEX (não base64)
    let encrypted = cipher.update(JSON.stringify(payload), 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    console.log('🔍 Backend - Dados criptografados (hex):', encrypted.substring(0, 30) + '...');
    console.log('🔍 Backend - Encrypted length:', encrypted.length);
    
    // IMPORTANTE: Concatenar IV(hex) + ':' + dados(hex)
    const combined = ivHex + ':' + encrypted;
    console.log('🔍 Backend - Combined (IV:encrypted):', combined.substring(0, 50) + '...');
    console.log('🔍 Backend - Combined length:', combined.length);
    
    // Converter combined string para base64url
    const base64 = Buffer.from(combined, 'utf8').toString('base64');
    const result = base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
    
    console.log('✅ Backend - Token final gerado:', result.substring(0, 20) + '...');
    console.log('🔍 Backend - Token length:', result.length);
    
    // TESTE: Verificar se pode decodificar corretamente
    const testDecode = Buffer.from(base64, 'base64').toString('utf8');
    const [testIV, testEncrypted] = testDecode.split(':');
    console.log('🧪 Backend - Test decode IV length:', testIV?.length); // Deve ser 32
    console.log('🧪 Backend - Test decode IV:', testIV?.substring(0, 10) + '...');
    
    if (testIV?.length !== 32) {
      throw new Error(`ERRO: IV decodificado tem tamanho ${testIV?.length}, esperado: 32`);
    }
    
    return result;
    
  } catch (error) {
    console.error('❌ Backend - Erro na criptografia:', error);
    throw new Error('Falha na criptografia das credenciais: ' + (error as Error).message);
  }
}

/**
 * Gera link de auto-login para o cliente (USANDO MESMA LÓGICA DO SCRIPT)
 */
async function gerarLinkAutoLogin(cliente: any): Promise<string | null> {
  try {
    // Verificar se cliente já tem link válido
    const existingShortUrl = await prisma.shortUrl.findFirst({
      where: { 
        clienteId: cliente.id,
        isActive: true
      }
    });
    
    if (existingShortUrl && cliente.autoLoginUrl) {
      console.log(`🔗 Cliente já possui link: ${cliente.autoLoginUrl}`);
      return cliente.autoLoginUrl;
    }
    
    // Validar se tem numeroDocumento
    if (!cliente.numeroDocumento) {
      throw new Error('Cliente não possui número de documento para gerar senha');
    }
    
    // Gerar token criptografado (IGUAL AO SCRIPT)
    const longToken = encryptCredentials(cliente.identificador, cliente.numeroDocumento);
    
    // Gerar código curto único (IGUAL AO SCRIPT)
    const shortCode = await generateUniqueShortCode();
    
    // URL final
    const autoLoginUrl = `${BASE_URL}/${shortCode}`;
    
    if (existingShortUrl) {
      // Atualizar link existente
      await prisma.shortUrl.update({
        where: { id: existingShortUrl.id },
        data: {
          shortCode,
          longToken,
          usageCount: 0,
          isActive: true
        }
      });
      console.log(`🔄 Link atualizado: ${autoLoginUrl}`);
    } else {
      // Criar novo link
      await prisma.shortUrl.create({
        data: {
          shortCode,
          longToken,
          clienteId: cliente.id,
          expiresAt: null, // Links permanentes
          usageCount: 0,
          isActive: true
        }
      });
      console.log(`🆕 Link criado: ${autoLoginUrl}`);
    }
    
    // Atualizar cliente com o link
    await prisma.cliente.update({
      where: { id: cliente.id },
      data: { autoLoginUrl }
    });
    
    console.log(`🔗 Link gerado: ${autoLoginUrl}`);
    return autoLoginUrl;
    
  } catch (error) {
    console.error('Erro ao gerar link:', error);
    throw error;
  }
}

// Função para reformatar número (copiada do script atualizarCamposChatguru.ts)
function reformatChatNumber(chatNumber: string): string {
  if (!chatNumber || typeof chatNumber !== "string") {
    throw new Error("Número de chat inválido");
  }

  const countryCode = chatNumber.slice(0, 2); // Código do país
  const areaCode = chatNumber.slice(2, 4); // Código de área
  let phoneNumber = chatNumber.slice(4);

  if (phoneNumber.length === 9) {
    phoneNumber = phoneNumber.slice(1);
  } else if (phoneNumber.length === 8) {
    phoneNumber = "9" + phoneNumber;
  }

  return countryCode + areaCode + phoneNumber;
}

// Função para atualizar MÚLTIPLOS campos de uma só vez (copiada do script)
async function updateMultipleChatGuruFields(
  chatNumber: string,
  fields: Record<string, string> // Ex: { IDC: "valor1", Senha_Cliente: "valor2" }
): Promise<any> {
  console.log(`🔄 Atualizando múltiplos campos para: ${chatNumber}, Campos:`, Object.keys(fields));

  // Remover caracteres especiais e espaços
  let cleanNumber = chatNumber.replace(/\D/g, "");
  
  // Garantir que o número tenha o formato correto (*************)
  if (!cleanNumber.startsWith("55")) {
    cleanNumber = "55" + cleanNumber;
  }

  const key = process.env.CHATGURU_API_KEY;
  const accountId = process.env.CHATGURU_ACCOUNT_ID;
  const phoneId = process.env.CHATGURU_PHONE_ID;
  const action = "chat_update_custom_fields";

  if (!key || !accountId || !phoneId) {
    throw new Error("Credenciais do Chatguru não configuradas");
  }

  // Função para executar diálogo
  const executeDialog = async (formattedNumber: string, retryCount: number = 0): Promise<void> => {
    try {
      console.log(`🎭 Executando diálogo para número: ${formattedNumber}`);
      
      const dialogId = "663e65dfa00a441168e435f4";
      const dialogAction = "dialog_execute";
      
      const chatGuruData = {
        key,
        account_id: accountId,
        phone_id: phoneId,
        dialog_id: dialogId,
        chat_number: formattedNumber,
        action: dialogAction,
      };

      console.log("🎭 Dados do diálogo:", {
        ...chatGuruData,
        key: "***",
        account_id: "***",
        phone_id: "***",
      });

      const formData = querystring.stringify(chatGuruData);
      
      const response = await axios.post(
        process.env.CHATGURU_API_ENDPOINT || "https://s16.chatguru.app/api/v1",
        formData,
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );

      console.log(`🎭 Resposta do diálogo (${response.status}):`, response.data);
      console.log("✅ Diálogo executado com sucesso para:", formattedNumber);

    } catch (error: any) {
      if (error.response && error.response.status === 400 && retryCount < 2) {
        console.warn(`⚠️ Erro 400 no diálogo, tentando reformatar número (tentativa ${retryCount + 1})`);
        const reformattedChatNumber = reformatChatNumber(formattedNumber);
        if (reformattedChatNumber !== formattedNumber) {
          console.log("🔄 Tentando diálogo com número reformatado:", reformattedChatNumber);
          return await executeDialog(reformattedChatNumber, retryCount + 1);
        }
      }
      console.error("❌ Erro ao executar diálogo no Chatguru:", error.response?.data || error.message);
      // Não quebrar o fluxo principal se o diálogo falhar
      console.warn("⚠️ Continuando processamento mesmo com falha no diálogo");
    }
  };

  const sendRequest = async (formattedNumber: string, retryCount: number = 0): Promise<any> => {
    try {
      console.log(`📤 Tentando atualizar campos para número: ${formattedNumber} (tentativa ${retryCount + 1})`);

      const chatGuruData: any = {
        key,
        account_id: accountId,
        phone_id: phoneId,
        chat_number: formattedNumber,
        action,
      };
      
      // Adicionar todos os campos dinamicamente
      Object.entries(fields).forEach(([fieldName, fieldValue]) => {
        chatGuruData[`field__${fieldName}`] = fieldValue;
      });

      console.log("🔑 Dados da requisição:", {
        ...chatGuruData,
        key: "***",
        account_id: "***",
        phone_id: "***",
      });

      const formData = querystring.stringify(chatGuruData);
      
      const response = await axios.post(
        process.env.CHATGURU_API_ENDPOINT || "https://s16.chatguru.app/api/v1",
        formData,
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );

      console.log(`📥 Resposta do Chatguru (${response.status}):`, response.data);
      console.log("✅ Múltiplos campos atualizados com sucesso para:", formattedNumber);

      // Aguardar 2 segundos antes de executar o diálogo
      console.log("⏳ Aguardando 2 segundos antes de executar diálogo...");
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Executar diálogo após sucesso na atualização dos campos
      await executeDialog(formattedNumber, retryCount);

      return response.data;
    } catch (error: any) {
      if (error.response && error.response.status === 400 && retryCount < 2) {
        console.warn(`⚠️ Erro 400, tentando reformatar número (tentativa ${retryCount + 1})`);
        const reformattedChatNumber = reformatChatNumber(formattedNumber);
        if (reformattedChatNumber !== formattedNumber) {
          console.log("🔄 Tentando com número reformatado:", reformattedChatNumber);
          return await sendRequest(reformattedChatNumber, retryCount + 1);
        }
      }
      console.error("❌ Erro na requisição ao Chatguru:", error.response?.data || error.message);
      throw error;
    }
  };

  return await sendRequest(cleanNumber);
}

function extrairUltimos3Digitos(documento: string): string {
  const apenasNumeros = documento.replace(/\D/g, '');
  return apenasNumeros.slice(-3);
}

function extrairShortCodeDaUrl(url: string): string {
  console.log("url", url);
  if (!url) return '';
  
  // Extrair apenas o código após a última barra
  // Ex: "https://cliente.registre.se/Kk0jjB" → "Kk0jjB"
  const shortCode = url.split('/').pop() || '';
  console.log("shortCode", shortCode);
  console.log(`🔗 Extraindo short code: "${url}" → "${shortCode}"`);
  return shortCode;
}

function obterListaTelefones(contatos: any[]): string[] {
  const telefones: string[] = [];
  
  for (const contato of contatos) {
    if (contato.telefone) {
      telefones.push(contato.telefone);
    }
    if (contato.telefoneSegundario) {
      telefones.push(contato.telefoneSegundario);
    }
  }
  
  // Remover duplicatas e números inválidos
  return telefones.filter((telefone, index, arr) => 
    arr.indexOf(telefone) === index && telefone.replace(/\D/g, '').length >= 10
  );
}

// Função para buscar persons por telefone no CRM
async function buscarPersonsPorTelefone(telefone: string): Promise<number[]> {
  try {
    const crmToken = process.env.CRM_TOKEN;
    if (!crmToken) {
      throw new Error("CRM_TOKEN não configurada");
    }

    // Normalizar telefone para busca (remover caracteres especiais)
    const telefoneNormalizado = telefone.replace(/\D/g, '');
    console.log(`🔍 Buscando persons para telefone: ${telefoneNormalizado}`);

    const url = `https://api.pipe.run/v1/contactPhones?show=200&phone=${telefoneNormalizado}&cursor=1`;
    
    const response = await axios.get(url, {
      headers: {
        token: crmToken,
      },
      timeout: 15000,
    });

    if (!response.data.success) {
      throw new Error(`Erro da API: ${response.data.message}`);
    }

    // Extrair person_ids únicos
    const personIds = new Set<number>();
    
    for (const contact of response.data.data) {
      if (contact.person_id) {
        personIds.add(contact.person_id);
      }
    }

    const personIdsArray = Array.from(personIds);
    console.log(`📞 Telefone ${telefoneNormalizado}: encontradas ${personIdsArray.length} persons [${personIdsArray.join(', ')}]`);
    
    return personIdsArray;

  } catch (error: any) {
    console.error(`❌ Erro ao buscar persons para telefone ${telefone}:`, error.message);
    return [];
  }
}

// Função para atualizar campo personalizado de uma person
async function atualizarCampoPersonalizado(personId: number, linkEncurtado: string): Promise<boolean> {
  try {
    const crmToken = process.env.CRM_TOKEN;
    if (!crmToken) {
      throw new Error("CRM_TOKEN não configurada");
    }

    console.log(`🔄 Atualizando person ${personId} com link: ${linkEncurtado}`);

    const url = `https://api.pipe.run/v1/persons/${personId}`;
    
    const payload = {
      custom_fields: [
        {
          id: 715633,
          value: linkEncurtado
        }
      ]
    };

    const response = await axios.put(url, payload, {
      headers: {
        'accept': 'application/json',
        'content-type': 'application/json',
        'token': crmToken,
      },
      timeout: 15000,
    });
    console.log("response", response.data.data);
    if (response.status === 200 || response.status === 201) {
      console.log(`✅ Person ${personId} atualizada com sucesso`);
      return true;
    } else {
      console.log(`⚠️ Person ${personId}: resposta inesperada (${response.status})`);
      return false;
    }

  } catch (error: any) {
    console.error(`❌ Erro ao atualizar person ${personId}:`, error.message);
    return false;
  }
}

// Função principal para atualizar todas as persons relacionadas aos telefones do cliente
async function atualizarPersonsRelacionadas(cliente: any): Promise<{
  sucesso: boolean;
  telefonesProcesados: number;
  personsAtualizadas: number;
  personsComErro: number;
}> {
  console.log(`🎯 Iniciando atualização de persons relacionadas ao cliente ID: ${cliente.id}`);
  
  try {
    // 1. Obter telefones do cliente
    const contatos = await prisma.contatoCliente.findMany({
      where: { clienteId: cliente.id }
    });
    
    const telefones = obterListaTelefones(contatos);
    
    if (telefones.length === 0) {
      console.log('📱 Nenhum telefone válido encontrado para o cliente');
      return { sucesso: true, telefonesProcesados: 0, personsAtualizadas: 0, personsComErro: 0 };
    }

    console.log(`📱 ${telefones.length} telefones para processar: ${telefones.map(t => t.substring(0, 5) + '****').join(', ')}`);

    // 2. Extrair link encurtado
    const linkEncurtado = cliente.autoLoginUrl || '';
    
    if (!linkEncurtado) {
      console.log('🔗 Nenhum link válido para atualizar');
      return { sucesso: true, telefonesProcesados: 0, personsAtualizadas: 0, personsComErro: 0 };
    }

    console.log(`🔗 Link encurtado a ser aplicado: "${linkEncurtado}"`);

    // 3. Coletar todas as persons únicas de todos os telefones
    const todasPersonsUnicas = new Set<number>();
    let telefonesProcesados = 0;

    for (const telefone of telefones) {
      const personIds = await buscarPersonsPorTelefone(telefone);
      
      personIds.forEach(id => todasPersonsUnicas.add(id));
      telefonesProcesados++;

      // Pequena pausa entre buscas para não sobrecarregar API
      if (telefonesProcesados < telefones.length) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    const personsParaAtualizar = Array.from(todasPersonsUnicas);
    console.log(`👥 Total de persons únicas encontradas: ${personsParaAtualizar.length} [${personsParaAtualizar.join(', ')}]`);

    if (personsParaAtualizar.length === 0) {
      console.log('👤 Nenhuma person encontrada para atualizar');
      return { sucesso: true, telefonesProcesados, personsAtualizadas: 0, personsComErro: 0 };
    }

    // 4. Atualizar cada person
    let personsAtualizadas = 0;
    let personsComErro = 0;

    for (let i = 0; i < personsParaAtualizar.length; i++) {
      const personId = personsParaAtualizar[i];
      console.log(`👤 Processando person ${i + 1}/${personsParaAtualizar.length}: ID ${personId}`);
      
      const sucesso = await atualizarCampoPersonalizado(personId, linkEncurtado);
      console.log("sucesso", sucesso);
      if (sucesso) {
        personsAtualizadas++;
      } else {
        personsComErro++;
      }

      // Pausa entre atualizações para não sobrecarregar API
      if (i < personsParaAtualizar.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 800));
      }
    }

    console.log(`🎯 Resumo: ${personsAtualizadas} persons atualizadas, ${personsComErro} com erro`);

    return {
      sucesso: true,
      telefonesProcesados,
      personsAtualizadas,
      personsComErro
    };

  } catch (error: any) {
    console.error(`❌ Erro geral na atualização de persons relacionadas:`, error.message);
    return { sucesso: false, telefonesProcesados: 0, personsAtualizadas: 0, personsComErro: 0 };
  }
}

/**
 * Busca e concatena os nomes das marcas de todos os processos do cliente
 */
async function obterNomesMarcasCliente(clienteId: number): Promise<string> {
  try {
    console.log(`🏷️ Buscando marcas do cliente ${clienteId}...`);
    
    // Buscar todos os processos do cliente com suas marcas
    const processos = await prisma.processo.findMany({
      where: { 
        clienteId: clienteId 
      },
      include: {
        marca: true
      }
    });
    
    console.log(`📋 Encontrados ${processos.length} processo(s) para o cliente ${clienteId}`);
    
    // Extrair nomes das marcas que têm nome preenchido
    const nomesMarcas: string[] = [];
    
    for (const processo of processos) {
      if (processo.marca && processo.marca.nome) {
        const nomeMarca = processo.marca.nome.trim();
        if (nomeMarca && !nomesMarcas.includes(nomeMarca)) {
          nomesMarcas.push(nomeMarca);
          console.log(`   ✅ Marca encontrada: "${nomeMarca}" (Processo: ${processo.numero})`);
        }
      } else {
        console.log(`   ⚠️ Processo ${processo.numero} sem marca ou sem nome de marca`);
      }
    }
    
    // Concatenar nomes das marcas com " / "
    const resultado = nomesMarcas.length > 0 ? nomesMarcas.join(' / ') : '';
    
    console.log(`🎯 Resultado final para Nome_da_marca: "${resultado}"`);
    console.log(`📊 Total de marcas únicas: ${nomesMarcas.length}`);
    
    return resultado;
    
  } catch (error) {
    console.error(`❌ Erro ao buscar marcas do cliente ${clienteId}:`, error);
    return ''; // Retornar string vazia em caso de erro
  }
}

/**
 * Atualiza campos no ChatGuru (versão corrigida usando a API real do ChatGuru)
 */
async function atualizarChatGuru(cliente: any, marcasDoGrupo?: string[]): Promise<{ sucesso: boolean, telefone?: string }> {
  console.log(`🔄 Processando cliente ID: ${cliente.id} para ChatGuru`);
  
  // Buscar telefones do cliente
  const contatos = await prisma.contatoCliente.findMany({
    where: { clienteId: cliente.id }
  });
  
  // Obter lista de telefones válidos
  const telefones = obterListaTelefones(contatos);
  
  if (telefones.length === 0) {
    throw new Error('Nenhum telefone válido disponível para atualizar ChatGuru');
  }

  console.log(`📱 Telefones disponíveis: ${telefones.length} (${telefones.map(t => t.substring(0, 5) + '****').join(', ')})`);

  // Preparar dados dos campos - TODOS DE UMA VEZ
  const senhaCliente = cliente.numeroDocumento ? 
    extrairUltimos3Digitos(cliente.numeroDocumento) : 
    '123';
  
  // 🆕 Usar marcas específicas do grupo ou buscar todas as marcas do cliente
  let nomesMarcas: string;
  if (marcasDoGrupo && marcasDoGrupo.length > 0) {
    // Remover duplicatas e juntar com " / "
    const marcasUnicas = [...new Set(marcasDoGrupo.filter(marca => marca && marca.trim()))];
    nomesMarcas = marcasUnicas.join(' / ');
    console.log(`🏷️ Usando marcas do grupo atual: ${nomesMarcas}`);
  } else {
    // Fallback para comportamento anterior
    nomesMarcas = "-";
    console.log(`🏷️ Usando "-" (fallback): ${nomesMarcas}`);
  }
  
  const camposParaAtualizar = {
    IDC: cliente.identificador,
    Senha_Cliente: senhaCliente,
    Link_Cliente: extrairShortCodeDaUrl(cliente.autoLoginUrl || ''),
    Nome_da_marca: nomesMarcas
  };

  console.log(`📋 Campos a atualizar:`, {
    IDC: cliente.identificador,
    Senha_Cliente: senhaCliente,
    Link_Cliente: extrairShortCodeDaUrl(cliente.autoLoginUrl || ''),
    Nome_da_marca: nomesMarcas
  });

  // Tentar atualizar campos para cada telefone até conseguir
  for (let i = 0; i < telefones.length; i++) {
    const telefone = telefones[i];
    console.log(`📞 Tentando telefone ${i + 1}/${telefones.length}: ${telefone.substring(0, 5)}****`);
    
    try {
      // Atualizar TODOS OS CAMPOS de uma só vez usando a API real do ChatGuru
      await updateMultipleChatGuruFields(telefone, camposParaAtualizar);
      
      // Se chegou aqui, todos os campos foram atualizados com sucesso
      console.log(`✅ ChatGuru atualizado com sucesso para ${telefone.substring(0, 5)}****`);
      return { sucesso: true, telefone };
      
    } catch (error: any) {
      console.log(`❌ Falha no telefone ${telefone.substring(0, 5)}****: ${error.message}`);
      
      // Se não é o último telefone, continuar tentando
      if (i < telefones.length - 1) {
        console.log(`🔄 Tentando próximo telefone...`);
        // Pequeno delay antes de tentar próximo telefone
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }
  }
  
  throw new Error(`Falha ao atualizar ChatGuru em todos os ${telefones.length} telefones`);
}

/**
 * Processa um protocolo da fila
 */
export async function processarProtocolo(protocolo: any): Promise<void> {
  console.log(`🚀 Iniciando processamento do protocolo do processo: ${protocolo.numeroProcesso}`);
  
  try {
    // 1. Atualizar status para PROCESSANDO
    await prisma.processamentoProtocolo.update({
      where: { id: protocolo.id },
      data: { 
        status: 'PROCESSANDO',
        tentativas: protocolo.tentativas + 1
      }
    });
    
    // 2. Buscar dados no CRM
    let dadosCrm: DadosCrm | null = null;
    try {
      dadosCrm = await buscarDadosCrm(protocolo.numeroProcesso);
      
      if (!dadosCrm) {
        throw new Error('Protocolo não encontrado no CRM');
      }
      
      await prisma.processamentoProtocolo.update({
        where: { id: protocolo.id },
        data: {
          crmDealId: dadosCrm.id,
          crmPersonId: dadosCrm.person?.id,
          crmCompanyId: dadosCrm.company?.id,
          dadosCrm: dadosCrm as any
        }
      });
      
    } catch (error) {
      await prisma.processamentoProtocolo.update({
        where: { id: protocolo.id },
        data: { 
          status: 'FALHA_CRM',
          ultimoErro: error instanceof Error ? error.message : 'Erro no CRM'
        }
      });
      throw error;
    }
    
    // 3. Criar/atualizar cliente
    let cliente: any;
    let clienteCriado = false;
    let clienteAtualizado = false;
    
    try {
      const resultado = await criarOuAtualizarCliente(dadosCrm);
      cliente = resultado.cliente;
      clienteCriado = resultado.criado;
      clienteAtualizado = resultado.atualizado;
      
      await prisma.processamentoProtocolo.update({
        where: { id: protocolo.id },
        data: {
          clienteId: cliente.id,
          clienteCriado,
          clienteAtualizado
        }
      });
      
    } catch (error) {
      await prisma.processamentoProtocolo.update({
        where: { id: protocolo.id },
        data: { 
          status: 'FALHA_CLIENTE',
          ultimoErro: error instanceof Error ? error.message : 'Erro ao criar cliente'
        }
      });
      throw error;
    }
    
    // 4. Conectar cliente ao processo existente
    let processo: any;
    try {
      processo = await conectarClienteAoProcesso(
        cliente.id, 
        protocolo.numeroProcesso,
        dadosCrm, // Dados do CRM para análise caso o processo não exista
        protocolo.dadosTitulares // Dados dos titulares já extraídos do PDF
      );
      
      await prisma.processamentoProtocolo.update({
        where: { id: protocolo.id },
        data: {
          processoId: processo.id,
          processoVinculado: true
        }
      });
      
    } catch (error) {
      await prisma.processamentoProtocolo.update({
        where: { id: protocolo.id },
        data: { 
          status: 'FALHA_PROCESSO',
          ultimoErro: error instanceof Error ? error.message : 'Erro ao criar processo'
        }
      });
      throw error;
    }
    
    // 5. Gerar link de auto-login
    let linkGerado = false;
    let autoLoginUrl: string | null = null;
    
    try {
      autoLoginUrl = await gerarLinkAutoLogin(cliente);
      
      await prisma.processamentoProtocolo.update({
        where: { id: protocolo.id },
        data: {
          linkGerado: !!autoLoginUrl,
          autoLoginUrl
        }
      });
      
      linkGerado = !!autoLoginUrl;
      
      // IMPORTANTE: Atualizar o objeto cliente com o link gerado
      if (autoLoginUrl) {
        cliente.autoLoginUrl = autoLoginUrl;
        console.log(`🔄 Cliente atualizado com autoLoginUrl: ${autoLoginUrl}`);
      }
      
    } catch (error) {
      await prisma.processamentoProtocolo.update({
        where: { id: protocolo.id },
        data: { 
          status: 'FALHA_LINK',
          ultimoErro: error instanceof Error ? error.message : 'Erro ao gerar link'
        }
      });
      throw error;
    }
    
    // 6. Atualizar ChatGuru
    let chatguruAtualizado = false;
    let telefoneChatguru: string | undefined;
    
    try {
      const resultado = await atualizarChatGuru(cliente);
      chatguruAtualizado = resultado.sucesso;
      telefoneChatguru = resultado.telefone;
      
      await prisma.processamentoProtocolo.update({
        where: { id: protocolo.id },
        data: {
          chatguruAtualizado,
          telefoneChatguru
        }
      });
      
    } catch (error) {
      await prisma.processamentoProtocolo.update({
        where: { id: protocolo.id },
        data: { 
          status: 'FALHA_CHATGURU',
          ultimoErro: error instanceof Error ? error.message : 'Erro no ChatGuru'
        }
      });
      throw error;
    }

    // 6.5. Atualizar persons relacionadas no CRM
    try {
      console.log('🔄 Iniciando atualização de persons relacionadas no CRM...');
      
      const resultadoPersons = await atualizarPersonsRelacionadas(cliente);
      const personsAtualizadas = resultadoPersons.personsAtualizadas;
      const personsComErro = resultadoPersons.personsComErro;
      
      console.log(`✅ Persons CRM: ${personsAtualizadas} atualizadas, ${personsComErro} com erro`);
      
    } catch (error) {
      console.error('❌ Erro na atualização de persons do CRM:', error);
      // Não quebrar o fluxo principal, apenas logar o erro
    }

    // 6.6. Atualizar status da oportunidade no CRM para "Ganha"
    if (dadosCrm?.id) {
      try {
        console.log(`🎯 Atualizando oportunidade ${dadosCrm.id} no CRM para status "Ganha"...`);
        
        // Importar funções do CRM
        const { getCRMConfiguration, updateDealStatus } = await import('../utils/crm.utils');
        
        // Obter configuração do CRM
        const { CRM_API_URL, CRM_TOKEN } = await getCRMConfiguration();
        
        // Atualizar status para "Ganha"
        await updateDealStatus(dadosCrm.id.toString(), CRM_API_URL, CRM_TOKEN);
        
        console.log(`✅ Oportunidade ${dadosCrm.id} marcada como "Ganha" no CRM`);
        
      } catch (crmError) {
        console.error(`❌ Erro ao atualizar status no CRM:`, crmError);
        // Não quebrar o fluxo, apenas logar o erro
        // O processamento continua mesmo se a atualização do CRM falhar
      }
    } else {
      console.log(`ℹ️ Nenhum ID de oportunidade CRM disponível para atualizar`);
    }
    
    // 7. Finalizar com sucesso
    await prisma.processamentoProtocolo.update({
      where: { id: protocolo.id },
      data: {
        status: 'SUCESSO',
        processadoEm: new Date(),
        ultimoErro: null
      }
    });
    
    console.log(`✅ Protocolo ${protocolo.numeroProcesso} processado com sucesso!`);
    
  } catch (error) {
    console.error(`❌ Erro no processamento do protocolo ${protocolo.numeroProcesso}:`, error);
    
    // Se não foi um erro específico (CRM, Cliente, etc), marcar como falha geral
    const protocoloAtual = await prisma.processamentoProtocolo.findUnique({
      where: { id: protocolo.id }
    });
    
    if (protocoloAtual?.status === 'PROCESSANDO') {
      await prisma.processamentoProtocolo.update({
        where: { id: protocolo.id },
        data: { 
          status: 'FALHA_GERAL',
          ultimoErro: error instanceof Error ? error.message : 'Erro desconhecido'
        }
      });
    }
    
    throw error;
  }
}

/**
 * Job principal que processa protocolos pendentes
 */
export async function processarFilaProtocolos(): Promise<void> {
  console.log('🔄 Iniciando job de processamento de protocolos...');
  
  try {
    // Buscar protocolos pendentes ou com falha (para retry)
    const agora = new Date();
    const protocolos = await prisma.processamentoProtocolo.findMany({
      where: {
        OR: [
          { status: 'PENDENTE' },
          {
            AND: [
              { status: { in: ['FALHA_CRM', 'FALHA_CLIENTE', 'FALHA_PROCESSO', 'FALHA_LINK', 'FALHA_CHATGURU', 'FALHA_GERAL'] } },
              { tentativas: { lt: 3 } },
              {
                OR: [
                  { proximaTentativa: { lte: agora } },
                  { proximaTentativa: null }
                ]
              }
            ]
          }
        ]
      },
      orderBy: { criadoEm: 'asc' },
      take: 50 // Processar máximo 50 por vez
    });
    
    if (protocolos.length === 0) {
      console.log('📋 Nenhum protocolo pendente encontrado');
      return;
    }
    
    console.log(`📋 Encontrados ${protocolos.length} protocolos para processar`);
    
    // Processar cada protocolo
    for (const protocolo of protocolos) {
      try {
        await processarProtocolo(protocolo);
        
        // Delay entre processamentos para não sobrecarregar APIs
        await new Promise(resolve => setTimeout(resolve, 2000));
        
      } catch (error) {
        // Definir próxima tentativa se ainda pode tentar
        if (protocolo.tentativas < 2) {
          const proximaTentativa = new Date();
          proximaTentativa.setMinutes(proximaTentativa.getMinutes() + (protocolo.tentativas + 1) * 5);
          
          await prisma.processamentoProtocolo.update({
            where: { id: protocolo.id },
            data: { proximaTentativa }
          });
        }
        
        console.error(`❌ Falha no protocolo ${protocolo.numeroProcesso}:`, error);
        continue;
      }
    }
    
    console.log('✅ Job de processamento concluído');
    
  } catch (error) {
    console.error('❌ Erro no job de processamento:', error);
  }
}

// ==================================================================================
// 🆕 NOVAS FUNCIONALIDADES: AGRUPAMENTO E PROCESSAMENTO OTIMIZADO POR CLIENTE
// ==================================================================================

/**
 * Interface para grupo de cliente
 */
interface GrupoCliente {
  clienteKey: string;
  clienteInfo: {
    tipo: 'person' | 'company';
    id: number;
    nome: string;
  };
  protocolos: any[];
  dadosCrm: DadosCrm;
}

/**
 * Interface para resultado do processamento de grupo
 */
interface ResultadoProcessamentoGrupo {
  clienteKey: string;
  clienteNome: string;
  protocolosTotal: number;
  protocolosProcessados: number;
  protocolosComErro: number;
  clienteCriado: boolean;
  clienteAtualizado: boolean;
  linkGerado: boolean;
  chatguruAtualizado: boolean;
  personsAtualizadas: number;
  marcasEncontradas: string[];
  erros: string[];
  sucesso: boolean;
  tempoProcessamento: number;
}

/**
 * Agrupa protocolos por cliente baseado nos dados do CRM
 * REUTILIZA a função buscarDadosCrm() existente
 */
export async function agruparProtocolosPorCliente(protocolos: any[]): Promise<Map<string, GrupoCliente>> {
  console.log(`\n🎯 ========== INICIANDO AGRUPAMENTO DE ${protocolos.length} PROTOCOLOS ==========`);
  
  const grupos = new Map<string, GrupoCliente>();
  const protocolosSemCliente: any[] = [];
  
  for (let i = 0; i < protocolos.length; i++) {
    const protocolo = protocolos[i];
    console.log(`\n🔍 Analisando protocolo ${i + 1}/${protocolos.length}: ${protocolo.numeroProcesso}`);
    
    try {
      // REUTILIZAR função existente para buscar dados no CRM
      const dadosCrm = await buscarDadosCrm(protocolo.numeroProcesso);
      
      if (!dadosCrm) {
        console.log(`   ⚠️ Protocolo ${protocolo.numeroProcesso} não encontrado no CRM - será processado individualmente`);
        protocolosSemCliente.push(protocolo);
        continue;
      }
      
      // Identificar cliente (person ou company)
      let clienteInfo: GrupoCliente['clienteInfo'];
      let clienteKey: string;
      
      if (dadosCrm.person) {
        clienteInfo = {
          tipo: 'person',
          id: dadosCrm.person.id,
          nome: dadosCrm.person.name
        };
        clienteKey = `person-${dadosCrm.person.id}`;
      } else if (dadosCrm.company) {
        clienteInfo = {
          tipo: 'company',
          id: dadosCrm.company.id,
          nome: dadosCrm.company.name
        };
        clienteKey = `company-${dadosCrm.company.id}`;
      } else {
        console.log(`   ⚠️ Protocolo ${protocolo.numeroProcesso} sem person nem company - será processado individualmente`);
        protocolosSemCliente.push(protocolo);
        continue;
      }
      
      console.log(`   👤 Cliente identificado: ${clienteInfo.nome} (${clienteInfo.tipo} ID: ${clienteInfo.id})`);
      
      // Adicionar ao grupo existente ou criar novo grupo
      if (grupos.has(clienteKey)) {
        const grupo = grupos.get(clienteKey)!;
        grupo.protocolos.push({ ...protocolo, dadosCrm });
        console.log(`   📂 Adicionado ao grupo existente - Total: ${grupo.protocolos.length} protocolos`);
      } else {
        const novoGrupo: GrupoCliente = {
          clienteKey,
          clienteInfo,
          protocolos: [{ ...protocolo, dadosCrm }],
          dadosCrm
        };
        grupos.set(clienteKey, novoGrupo);
        console.log(`   📂 Novo grupo criado para: ${clienteInfo.nome}`);
      }
      
    } catch (error) {
      console.error(`   ❌ Erro ao buscar dados do protocolo ${protocolo.numeroProcesso} no CRM:`, error);
      protocolosSemCliente.push(protocolo);
    }
    
    // Pequeno delay para não sobrecarregar API do CRM
    if (i < protocolos.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }
  
  // Adicionar protocolos sem cliente como grupos individuais
  if (protocolosSemCliente.length > 0) {
    console.log(`\n🔄 Criando grupos individuais para ${protocolosSemCliente.length} protocolos sem cliente identificado`);
    
    for (let i = 0; i < protocolosSemCliente.length; i++) {
      const protocolo = protocolosSemCliente[i];
      const clienteKey = `orphan-${protocolo.numeroProcesso}`;
      
      const grupoOrfao: GrupoCliente = {
        clienteKey,
        clienteInfo: {
          tipo: 'person', // Default para orphans
          id: 0,
          nome: `Processo ${protocolo.numeroProcesso} (sem cliente)`
        },
        protocolos: [protocolo],
        dadosCrm: {} as DadosCrm // Vazio para orphans
      };
      
      grupos.set(clienteKey, grupoOrfao);
    }
  }
  
  // Relatório final do agrupamento
  console.log(`\n📊 ========== RELATÓRIO DO AGRUPAMENTO ==========`);
  console.log(`🎯 Total de grupos criados: ${grupos.size}`);
  console.log(`📋 Protocolos sem cliente: ${protocolosSemCliente.length}`);
  
  let totalProtocolosAgrupados = 0;
  for (const [key, grupo] of grupos) {
    console.log(`   📂 ${grupo.clienteInfo.nome}: ${grupo.protocolos.length} protocolo(s)`);
    totalProtocolosAgrupados += grupo.protocolos.length;
  }
  
  console.log(`✅ Total de protocolos agrupados: ${totalProtocolosAgrupados}`);
  console.log(`========== FIM DO AGRUPAMENTO ==========\n`);
  
  return grupos;
}

/**
 * Processa um grupo de cliente de forma otimizada
 * REUTILIZA todas as funções existentes sem modificá-las
 */
export async function processarGrupoCliente(grupo: GrupoCliente): Promise<ResultadoProcessamentoGrupo> {
  const inicioProcessamento = Date.now();
  const resultado: ResultadoProcessamentoGrupo = {
    clienteKey: grupo.clienteKey,
    clienteNome: grupo.clienteInfo.nome,
    protocolosTotal: grupo.protocolos.length,
    protocolosProcessados: 0,
    protocolosComErro: 0,
    clienteCriado: false,
    clienteAtualizado: false,
    linkGerado: false,
    chatguruAtualizado: false,
    personsAtualizadas: 0,
    marcasEncontradas: [],
    erros: [],
    sucesso: false,
    tempoProcessamento: 0
  };
  
  console.log(`\n🚀 ========== PROCESSANDO GRUPO: ${grupo.clienteInfo.nome} (${grupo.protocolos.length} protocolos) ==========`);
  
  try {
    // PARA GRUPOS ÓRFÃOS: Processar individualmente usando função existente
    if (grupo.clienteKey.startsWith('orphan-')) {
      console.log(`⚠️ Grupo órfão detectado - processando individualmente...`);
      
      for (const protocolo of grupo.protocolos) {
        try {
          await processarProtocolo(protocolo); // FUNÇÃO EXISTENTE
          resultado.protocolosProcessados++;
        } catch (error) {
          resultado.protocolosComErro++;
          resultado.erros.push(`Protocolo ${protocolo.numeroProcesso}: ${error}`);
        }
      }
      
      resultado.sucesso = resultado.protocolosProcessados > 0;
      resultado.tempoProcessamento = Date.now() - inicioProcessamento;
      return resultado;
    }
    
    // PARA GRUPOS COM CLIENTE IDENTIFICADO: Processamento otimizado
    const dadosCrmPrincipal = grupo.dadosCrm;
    
    // 1. CRIAR/ATUALIZAR CLIENTE (1x por grupo) - FUNÇÃO EXISTENTE
    console.log(`\n👤 ETAPA 1: Criando/atualizando cliente...`);
    let cliente: any;
    
    try {
      const resultadoCliente = await criarOuAtualizarCliente(dadosCrmPrincipal); // FUNÇÃO EXISTENTE
      cliente = resultadoCliente.cliente;
      resultado.clienteCriado = resultadoCliente.criado;
      resultado.clienteAtualizado = resultadoCliente.atualizado;
      
      console.log(`✅ Cliente: ${cliente.nome} (ID: ${cliente.id}) - ${resultado.clienteCriado ? 'CRIADO' : 'ATUALIZADO'}`);
    } catch (error) {
      resultado.erros.push(`Erro ao criar/atualizar cliente: ${error}`);
      throw error;
    }
    
    // 2. PROCESSAR TODOS OS PROTOCOLOS DO GRUPO (conectar processos)
    console.log(`\n📋 ETAPA 2: Conectando ${grupo.protocolos.length} processo(s) ao cliente...`);
    
    for (let i = 0; i < grupo.protocolos.length; i++) {
      const protocolo = grupo.protocolos[i];
      console.log(`   🔗 Processando protocolo ${i + 1}/${grupo.protocolos.length}: ${protocolo.numeroProcesso}`);
      
      try {
        // Atualizar status para PROCESSANDO
        await prisma.processamentoProtocolo.update({
          where: { id: protocolo.id },
          data: { 
            status: 'PROCESSANDO',
            tentativas: protocolo.tentativas + 1,
            clienteId: cliente.id,
            crmDealId: protocolo.dadosCrm.id,
            crmPersonId: protocolo.dadosCrm.person?.id,
            crmCompanyId: protocolo.dadosCrm.company?.id,
            dadosCrm: protocolo.dadosCrm as any
          }
        });
        
        // Conectar cliente ao processo - FUNÇÃO EXISTENTE
        const processo = await conectarClienteAoProcesso(
          cliente.id, 
          protocolo.numeroProcesso,
          protocolo.dadosCrm,
          protocolo.dadosTitulares
        );
        
        // Atualizar registro com sucesso na conexão
        await prisma.processamentoProtocolo.update({
          where: { id: protocolo.id },
          data: {
            processoId: processo.id,
            processoVinculado: true
          }
        });
        
                 // Marcar oportunidade como "Ganha" no CRM - FUNÇÃO EXISTENTE
         if (protocolo.dadosCrm?.id) {
           try {
             const { getCRMConfiguration, updateDealStatus } = await import('../utils/crm.utils');
             const { CRM_API_URL, CRM_TOKEN } = await getCRMConfiguration();
             await updateDealStatus(protocolo.dadosCrm.id.toString(), CRM_API_URL, CRM_TOKEN);
             console.log(`     🎯 Oportunidade ${protocolo.dadosCrm.id} marcada como "Ganha"`);
           } catch (crmError) {
             console.warn(`     ⚠️ Erro ao marcar oportunidade como ganha: ${crmError}`);
           }
         }
        
        resultado.protocolosProcessados++;
        console.log(`     ✅ Protocolo ${protocolo.numeroProcesso} conectado com sucesso`);
        
      } catch (error) {
        resultado.protocolosComErro++;
        resultado.erros.push(`Protocolo ${protocolo.numeroProcesso}: ${error}`);
        console.error(`     ❌ Erro no protocolo ${protocolo.numeroProcesso}:`, error);
        
        // Marcar protocolo com erro
        await prisma.processamentoProtocolo.update({
          where: { id: protocolo.id },
          data: { 
            status: 'FALHA_PROCESSO',
            ultimoErro: error instanceof Error ? error.message : 'Erro ao conectar processo'
          }
        });
      }
    }
    
    // 3. GERAR LINK AUTO-LOGIN (1x por grupo) - FUNÇÃO EXISTENTE
    console.log(`\n🔗 ETAPA 3: Gerando link de auto-login...`);
    
    try {
      const autoLoginUrl = await gerarLinkAutoLogin(cliente); // FUNÇÃO EXISTENTE
      resultado.linkGerado = !!autoLoginUrl;
      
      if (autoLoginUrl) {
        cliente.autoLoginUrl = autoLoginUrl;
        console.log(`✅ Link gerado: ${autoLoginUrl}`);
      }
    } catch (error) {
      resultado.erros.push(`Erro ao gerar link: ${error}`);
      console.error(`❌ Erro ao gerar link:`, error);
    }
    
    // 4. ATUALIZAR CHATGURU (1x por grupo com marcas específicas do grupo) - FUNÇÃO EXISTENTE
    console.log(`\n💬 ETAPA 4: Atualizando ChatGuru...`);
    
    try {
      // 🆕 Extrair marcas específicas dos protocolos do grupo
      const marcasDoGrupo: string[] = [];
      
      for (const protocolo of grupo.protocolos) {
        // Opção 1: Usar elementoNominativo (extraído no upload)
        if (protocolo.elementoNominativo) {
          marcasDoGrupo.push(protocolo.elementoNominativo);
        }
        
                 // Opção 2: Extrair do CRM se elementoNominativo não estiver disponível  
         if (!protocolo.elementoNominativo && protocolo.dadosCrm?.customFields) {
           const { fieldMapping } = await import('../utils/crm.utils');
           
           // Função auxiliar local para extrair campo customizado
           const extrairCampoCustomizado = (dadosCrm: any, fieldId: number): any => {
             const campo = dadosCrm.customFields?.find((field: any) => field.id === fieldId);
             return campo ? campo.value : null;
           };
           
           const nomeMarcaCrm = String(extrairCampoCustomizado(protocolo.dadosCrm, fieldMapping.nomeMarca.id) || '');
           if (nomeMarcaCrm) {
             marcasDoGrupo.push(nomeMarcaCrm);
           }
         }
      }
      
      // Remover duplicatas e valores vazios
      const marcasUnicasDoGrupo = [...new Set(marcasDoGrupo.filter(marca => marca && marca.trim()))];
      
      console.log(`🏷️ Marcas extraídas do grupo: [${marcasUnicasDoGrupo.join(', ')}]`);
      
      // 🆕 Passar marcas específicas do grupo para o ChatGuru
      const resultadoChatguru = await atualizarChatGuru(cliente, marcasUnicasDoGrupo);
      resultado.chatguruAtualizado = resultadoChatguru.sucesso;
      
      // Usar as marcas do grupo para o relatório
      resultado.marcasEncontradas = marcasUnicasDoGrupo;
      
      const nomesMarcasGrupo = marcasUnicasDoGrupo.join(' / ') || 'nenhuma';
      console.log(`✅ ChatGuru atualizado - Marcas do grupo: ${nomesMarcasGrupo}`);
    } catch (error) {
      resultado.erros.push(`Erro no ChatGuru: ${error}`);
      console.error(`❌ Erro no ChatGuru:`, error);
    }
    
    // 5. ATUALIZAR PERSONS RELACIONADAS (1x por grupo) - FUNÇÃO EXISTENTE
    console.log(`\n👥 ETAPA 5: Atualizando persons relacionadas no CRM...`);
    
    try {
      const resultadoPersons = await atualizarPersonsRelacionadas(cliente); // FUNÇÃO EXISTENTE
      resultado.personsAtualizadas = resultadoPersons.personsAtualizadas;
      
      console.log(`✅ Persons atualizadas: ${resultado.personsAtualizadas}`);
    } catch (error) {
      resultado.erros.push(`Erro nas persons: ${error}`);
      console.error(`❌ Erro nas persons:`, error);
    }
    
    // 6. FINALIZAR PROTOCOLOS PROCESSADOS COM SUCESSO
    console.log(`\n✅ ETAPA 6: Finalizando protocolos processados...`);
    
    const protocolosProcessadosComSucesso = grupo.protocolos.filter((p, index) => index < resultado.protocolosProcessados);
    
    for (const protocolo of protocolosProcessadosComSucesso) {
      try {
        await prisma.processamentoProtocolo.update({
          where: { id: protocolo.id },
          data: {
            status: 'SUCESSO',
            processadoEm: new Date(),
            ultimoErro: null,
            linkGerado: resultado.linkGerado,
            autoLoginUrl: cliente.autoLoginUrl,
            chatguruAtualizado: resultado.chatguruAtualizado
          }
        });
      } catch (error) {
        console.error(`⚠️ Erro ao finalizar protocolo ${protocolo.numeroProcesso}:`, error);
      }
    }
    
    resultado.sucesso = resultado.protocolosProcessados > 0;
    resultado.tempoProcessamento = Date.now() - inicioProcessamento;
    
    console.log(`\n🎊 ========== GRUPO FINALIZADO: ${grupo.clienteInfo.nome} ==========`);
    console.log(`📊 Sucessos: ${resultado.protocolosProcessados}, Erros: ${resultado.protocolosComErro}`);
    console.log(`💬 ChatGuru: ${resultado.chatguruAtualizado ? 'OK' : 'FALHA'}, Persons: ${resultado.personsAtualizadas}`);
    console.log(`⏱️ Tempo: ${resultado.tempoProcessamento}ms`);
    
    return resultado;
    
  } catch (error) {
    resultado.sucesso = false;
    resultado.tempoProcessamento = Date.now() - inicioProcessamento;
    resultado.erros.push(`Erro geral do grupo: ${error}`);
    
    console.error(`❌ ========== ERRO NO GRUPO: ${grupo.clienteInfo.nome} ==========`, error);
    
    return resultado;
  }
}

/**
 * Processa fila de protocolos com agrupamento por cliente
 * NOVA versão otimizada que usa agrupamento
 */
export async function processarFilaComAgrupamento(): Promise<{
  totalProtocolos: number;
  totalGrupos: number;
  gruposProcessados: number;
  gruposComErro: number;
  tempoTotal: number;
  resultados: ResultadoProcessamentoGrupo[];
}> {
  const inicioGeral = Date.now();
  console.log('\n🚀 ========== INICIANDO PROCESSAMENTO COM AGRUPAMENTO ==========');
  
  try {
    // Buscar protocolos pendentes (mesma lógica da função original)
    const agora = new Date();
    const protocolos = await prisma.processamentoProtocolo.findMany({
      where: {
        OR: [
          { status: 'PENDENTE' },
          {
            AND: [
              { status: { in: ['FALHA_CRM', 'FALHA_CLIENTE', 'FALHA_PROCESSO', 'FALHA_LINK', 'FALHA_CHATGURU', 'FALHA_GERAL'] } },
              { tentativas: { lt: 3 } },
              {
                OR: [
                  { proximaTentativa: { lte: agora } },
                  { proximaTentativa: null }
                ]
              }
            ]
          }
        ]
      },
      orderBy: { criadoEm: 'asc' },
      take: 50 // Processar máximo 50 por vez
    });
    
    if (protocolos.length === 0) {
      console.log('📋 Nenhum protocolo pendente encontrado');
      return {
        totalProtocolos: 0,
        totalGrupos: 0,
        gruposProcessados: 0,
        gruposComErro: 0,
        tempoTotal: Date.now() - inicioGeral,
        resultados: []
      };
    }
    
    console.log(`📋 Encontrados ${protocolos.length} protocolos para processar`);
    
    // Agrupar protocolos por cliente
    const grupos = await agruparProtocolosPorCliente(protocolos);
    
    // Processar cada grupo
    const resultados: ResultadoProcessamentoGrupo[] = [];
    let gruposProcessados = 0;
    let gruposComErro = 0;
    
    for (const [clienteKey, grupo] of grupos) {
      try {
        const resultado = await processarGrupoCliente(grupo);
        resultados.push(resultado);
        
        if (resultado.sucesso) {
          gruposProcessados++;
        } else {
          gruposComErro++;
        }
        
        // Delay entre grupos para não sobrecarregar APIs
        await new Promise(resolve => setTimeout(resolve, 3000));
        
      } catch (error) {
        gruposComErro++;
        console.error(`❌ Erro fatal no grupo ${grupo.clienteInfo.nome}:`, error);
        
        // Criar resultado de erro para o grupo
        resultados.push({
          clienteKey: grupo.clienteKey,
          clienteNome: grupo.clienteInfo.nome,
          protocolosTotal: grupo.protocolos.length,
          protocolosProcessados: 0,
          protocolosComErro: grupo.protocolos.length,
          clienteCriado: false,
          clienteAtualizado: false,
          linkGerado: false,
          chatguruAtualizado: false,
          personsAtualizadas: 0,
          marcasEncontradas: [],
          erros: [`Erro fatal: ${error}`],
          sucesso: false,
          tempoProcessamento: 0
        });
      }
    }
    
    const tempoTotal = Date.now() - inicioGeral;
    
    console.log(`\n🎊 ========== PROCESSAMENTO CONCLUÍDO ==========`);
    console.log(`📊 Total de protocolos: ${protocolos.length}`);
    console.log(`📂 Total de grupos: ${grupos.size}`);
    console.log(`✅ Grupos processados: ${gruposProcessados}`);
    console.log(`❌ Grupos com erro: ${gruposComErro}`);
    console.log(`⏱️ Tempo total: ${tempoTotal}ms`);
    
    return {
      totalProtocolos: protocolos.length,
      totalGrupos: grupos.size,
      gruposProcessados,
      gruposComErro,
      tempoTotal,
      resultados
    };
    
  } catch (error) {
    console.error('❌ Erro no processamento com agrupamento:', error);
    throw error;
  }
} 