import app from "./app";
import { verificarEAtualizarEstimativas } from "./services/atualizacaoEstimativasMerito";
import { iniciarJobProcessamentoProtocolos } from "./jobs/processamentoProtocoloJob";

const PORT = process.env.PORT || 3321;

app.listen(PORT, async () => {
  console.log(`Servidor rodando na porta ${PORT}`);
  await verificarEAtualizarEstimativas();
  
  // Iniciar job de processamento de protocolos
  iniciarJobProcessamentoProtocolos();
});
