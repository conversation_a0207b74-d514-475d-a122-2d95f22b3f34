 🚀 Iniciando processamento do protocolo 939551373
17|api-v3-rgsys  | 2025-06-12 08:42:13: 🔍 Buscando protocolo 939551373 no CRM...
17|api-v3-rgsys  | 2025-06-12 08:42:13: ✅ Encontrado deal no CRM: {
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "id": ********,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "hash": "9r9r3fgvv70o8048kggcoksoc04kcgk",
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "rdstation_reference": "(11) 97350-1535",
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "type_reference": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "reference": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "temperature": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "probability": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "account_id": 13955,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "pipeline_id": 56897,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "owner_id": 86126,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "stage_id": 341596,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "person_id": ********,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "company_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "lost_reason_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "origin_id": 566319,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "started_in_stage_id": 341590,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "created_at": "2025-06-11 17:27:11",
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "title": "[43] TACCHIO GELATO ARTIGIANALE",
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "description": "11:53 06/06 Integração: (11) 97350-1535",
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "observation": "11:53 06/06 Integração: (11) 97350-1535",
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "status": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "closed_at": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "reason_close": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "deleted": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "freezed": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "value": 2320,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "order": 2337,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "updated_at": "2025-06-12 08:29:14",
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "last_stage_updated_at": "2025-06-12 08:29:14",
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "value_mrr": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "probably_closed_at": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "last_contact_at": "2025-06-12 08:29:14",
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "stage_changed_at": "2025-06-12 08:29:13",
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "frozen_at": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "lead_time": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "customFields": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 244296,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "mobile_phone",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "022511644db7bc1d8b7b607ab750cedd",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "(11) 97350-1535",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "(11) 97350-1535",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 213657,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Nome da marca ✍️",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "40641d0630e125b85f1d91b2e469b2b4",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "TACCHIO Gelato Artigianale",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "TACCHIO Gelato Artigianale",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 627361,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Como nos conheceu",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "44eba4ee59f67419160ecd387d969728",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Indicação de um amigo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Indicação de um amigo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 225754,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Nome Lead",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "5e61ab1b7425f4ebae5b0b0aa0745227",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Murillo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Murillo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 547311,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Motivo do registro",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "f3d22c9aab2dbc4b38e70e11f764921a",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 594785,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Data e hora visita",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "b66315788c90e658c1571c7ca335a6e5",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 594786,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Data e hora conversão",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "b226951a1e722b87cb6bdf16e15893ef",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "2025-06-06T11:53:25-03:00",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "2025-06-06T11:53:25-03:00",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 613811,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Dispositivo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "3cbe466620723d4a0f2174e1d98a9d25",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 232453,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Canal",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "09ec28a498ad27023b130d8d1bac0230",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "/",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "/",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 388863,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Campanha",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "cf1ab73f8b0f611734b63f619ef95d73",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 631782,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "UTM Final",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "8cd3283b4af7d496cc84ff1f961207fe",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 480567,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "url_conversao",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "1f80ef556fd1adc9efb8a1b4f7d95031",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 5,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 631785,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Historico Campanhas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "c8ac93e1550815982f9030cd58b9d4f7",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 204059,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Lead Score",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "2efae4f52ad645c8fbbd483f586796f8",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "180",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "180",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 196895,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Indicado por",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "ff160b3013b1fb3818a9b250c689b339",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 623519,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Consentimento",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "b4c1336997972cb7722fc209e6e508c1",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "accepted",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "accepted",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 703558,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "mkt-trk",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "54a85cd08d7fc7f06963493dbf23acf3",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 5,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "{\"trfSrc\":\"\",\"rdTrk\":\"{\\\"id\\\":\\\"16ade25a-6814-446c-9c45-6934cb9556e1\\\"}\"}",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "{\"trfSrc\":\"\",\"rdTrk\":\"{\\\"id\\\":\\\"16ade25a-6814-446c-9c45-6934cb9556e1\\\"}\"}",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 229327,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": " Nome Lead",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "9ddab6c554b26f40c43f8adbc7d0814a",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Murillo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Murillo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 213595,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Estágio do negócio",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "b581f8cef83aaaa7332129dc2552dd3e",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Pretendo começar em breve",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Pretendo começar em breve",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Comecei recentemente",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Já atuo há mais de 6 meses",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Já atuo há mais de 2 anos",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Pretendo começar em breve",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Por enquanto é só um plano"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": "Pretendo começar em breve",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Pretendo começar em breve"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 370155,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "pipecanal",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "dbdb72e1e1947a63c4fc9f16af7836bf",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "/",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "/",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 521462,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "score",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "480ed6abb2be56750429335e98bbcbde",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "120",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "120",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 544091,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "fase-do-planejamento",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "383add8ec4fbeb309870653586e667cb",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Logotipo está pronto e quero registrar antes de iniciar a divulgação.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Logotipo está pronto e quero registrar antes de iniciar a divulgação.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Não tenho logotipo ainda, mas quero garantir a propriedade do nome.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Logotipo está pronto e quero registrar antes de iniciar a divulgação.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Confirmar se o nome está disponível, antes de criar o logotipo.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Somente saber se está disponível.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Se estiver disponível, quero registrar.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Verificar disponibilidade."
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": "Logotipo está pronto e quero registrar antes de iniciar a divulgação.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Logotipo está pronto e quero registrar antes de iniciar a divulgação."
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 244308,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Quantos colaboradores?",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "b0b3877ee5bde2ebc511f25210f4698c",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "1 a 2",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "1 a 2",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "1 a 2",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "3 a 5",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "6 a 10",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "11 a 50",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "+ de 50",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Mais de 50"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": "1 a 2",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "1 a 2"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 369377,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Possui CNPJ",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "83342876471f8508dd364202916d3b24",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Sim.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Sim.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Sim",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Não, mas já estamos providenciando",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Atuo como pessoa física",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Não, mas estamos providenciando.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Sim."
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": "Sim.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Sim."
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 213683,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "porte",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "3843ba7f86f1357c881fca480f1d0754",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 6,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Pessoa física"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "[\"Pessoa física\"]",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Pessoa física",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "MEI",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "ME",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "EPP",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Demais",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Inova Simples",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "CNPJ em criação",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Ainda não identificado",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Associação",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Não sei responder.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "DEMAIS"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Pessoa física"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Pessoa física"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 240584,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Tipo de negócio",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "f91331fcd8a32e775b1c7de2a4632ff5",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Outros",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Outros",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Vestuário",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Fabricação de produtos",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Comércio de produtos de outras marcas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Prestação de serviço",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Produtor de conteúdo ou produto digital",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Entidade social ou organização religiosa",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Outros",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Comércio de produtos de marca própria"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": "Outros",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Outros"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 196382,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Ramo de atuação",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "5e2ab920101b625d8fcc7d657bacae4c",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "gelateria",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "gelateria",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 244306,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "sua-marca-possui-site-ou-redes-sociais",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "d411c026484e16660fed999b270549c3",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Ainda não",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Ainda não",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 549882,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "IdOriginal",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "fe3516735e9a34b77b65a07a51255344",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 14,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "48743676",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": 48743676,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 594787,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Hora entrada lead",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "ccd495150cb952162f0b4342adc3a2a0",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "06/06/2025, 11:53:30",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "06/06/2025, 11:53:30",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 525692,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "PTime",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "5f009cc35dc1c8bdd8934a66e206732f",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "06/06/2025, 11:53:54",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "06/06/2025, 11:53:54",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 547312,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Grau de conhecimento",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "99903be8bba5e5a2081698321061b451",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 554713,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Em quanto tempo quer resolver",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "325820413d0fc4f14d4d1daa84feebcd",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 554712,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Quanto já investiu na marca",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "c10b17029a905dd53719e773936777a8",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 194463,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Classes ✍️",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "8b7244acfcc87d24fbeebb7286ab6423",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 6,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "43"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "[\"43\"]",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "1",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "2",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "3",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "4",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "5",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "6",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "7",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "8",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "9",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "10",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "11",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "12",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "13",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "14",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "15",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "16",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "17",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "18",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "19",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "20",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "21",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "22",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "23",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "24",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "25",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "26",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "27",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "28",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "29",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "30",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "31",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "32",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "33",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "34",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "35",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "36",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "37",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "38",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "39",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "40",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "41",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "42",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "43",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "44",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "45"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "43"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "43"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 194466,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Tipo de marca ✍️",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "9fac515f2eaffe31a354c86baed1cb9a",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 6,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Mista"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "[\"Mista\"]",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Nominativa",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Mista",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Figurativa"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Mista"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Mista"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 203714,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Site e/ou redes sociais",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "8626a4baa93f97d3bbf189ce631b1368",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 225833,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Tipo de serviço",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "f832a9f2ab8eca6ad2cf81cf15050ebb",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 6,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Registro de marca"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "[\"Registro de marca\"]",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Registro de marca",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Renovação de registro",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Atualização de registro",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Recurso administrativo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Manifestação à Oposição",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Contranotificação"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Registro de marca"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Registro de marca"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 225839,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Viabilidade de registro ✍️",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "fb4b71d1a007bcf589278560161ee30d",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Viável no conjunto misto",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Viável no conjunto misto",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Viável ✅",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Inviável 🚫",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Viável com restrições",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Viável em algumas classes",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Arriscado",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Viável no conjunto misto"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": "Viável no conjunto misto",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Viável no conjunto misto"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 474111,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Estratégia de atendimento e diagnóstico ✍️",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "ef2dfea143f848b044a09538a4a4a2a8",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 5,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Tem certo risco pela marca abaixo (tacchino), porém entendo que na forma mista será viável.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Tem certo risco pela marca abaixo (tacchino), porém entendo que na forma mista será viável.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 228243,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Como foi atendido? ✍️",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "a5236860f6af80fc755fb8be5b159c7d",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 6,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "WhatsApp"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "[\"WhatsApp\"]",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Ligação Telefônica",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "WhatsApp",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "E-mail",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Video conferência",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Presencial",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "PDF",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Impressa",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Instagram",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Linkedin",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Outra rede social"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "WhatsApp"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "WhatsApp"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 213682,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Negociação ✍️",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "8fde08d168df09b1f7b785d15ee90568",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Valor normal",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Valor normal",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Valor normal",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Valor promocional"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": "Valor normal",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Valor normal"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 512979,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Análise de Crédito ✍️",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "5e62d130552affa0e8e73b6f8387a554",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Não é necessário realizar a análise de crédito.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Não é necessário realizar a análise de crédito.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Recusado",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Arriscado - Necessário confirmar pagamento da entrada para que possamos realizar o protocolo.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Aprovado, pagamento de entrada em até 7 dias, protocolo padrão.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Aprovado ✚ Pagamento de entrada a combinar, protocolo padrão.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Não é necessário realizar a análise de crédito."
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": "Não é necessário realizar a análise de crédito.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Não é necessário realizar a análise de crédito."
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 213659,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Proposta no Qwril ✍️",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "33ff940ed3cc66bc790db9a913ce08f6",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Sim",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Não"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 213684,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Link Qwirl",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "236fb94f5d58a794692a3415ef35adf0",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 9,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 226320,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Especificações",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "b6c2ad9120aaf13d3e8374bd3d43ae8d",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 5,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Serviços de lanchonetes; \nServiços de restaurantes; bar",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Serviços de lanchonetes; \nServiços de restaurantes; bar",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 713085,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Parceiro",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "1f2bfb8f0db126c85ed4ef5f56704730",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "08 Design | Anderiquei",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Feitoria | Alex Reuter",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Bradda",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Cristalize Cosméticos",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Emoticomm",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Firmorama",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Floema | Gi Marques",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Juliana Amaral",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Lai Creative | Lais",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "GGE.Art Design | George Varela",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Daniel Nardes | Designer",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Duora | Ana Paula Rubini",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Lucas Balbinot | Designer",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Luis Ferreira | Designer",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Marco Buka | Designer",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Studio Flamma",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Thiago Boller | Designer",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Contasul | Jacira",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Aline Portela | Designer",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Adston Bazante | Designer",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Gransolução Assessoria Contábil | Breno",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Contabilizei",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "DÉDI Agência Criativa | Deise Santiago Bento",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "EMCAT Contabilidade | Edimilson Silva",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Japona Studio | Lucas Mujo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Marchezi Design | Jonathan",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Juninho Menegatti",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Maxseller | Rogério",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Tenório Redator",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Cameleum | Vinicius Tadeu"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 545920,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Plano",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "04462ea78bc1a4ecc2e76ac7129c5be9",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Pacote completo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Pacote completo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Pacote simples",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Pacote completo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Não se aplica",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Plano especial 🪄"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": "Pacote completo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Pacote completo"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 464564,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Protocolo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "ca9c59b7f0edc9abd6840c84ec16980b",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 6,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Padrão: protocolo depois do faturamento"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "[\"Padrão: protocolo depois do faturamento\"]",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Deluxe: protocolo autorizado",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Padrão: protocolo depois do faturamento",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "24h: faturamento e protocolo em até 24h",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Arriscado: protocolo somente após a confirmação do recebimento"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Padrão: protocolo depois do faturamento"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Padrão: protocolo depois do faturamento"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 538627,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Observações FATURAMENTO",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "b6e1ef5dff432ef3beb1423efcfa202c",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 5,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Seguir normalmente",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Seguir normalmente",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 464609,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Forma de pagamento",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "02735c2f9f01ea613f9d61a9650b7aa0",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "À vista",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "À vista",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "À vista",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Boleto Bancário",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Cartão de Crédito",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Condição Especial ✴️"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": "À vista",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "À vista"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 213681,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Valor a cobrar",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "452e46be16581bc793d3d5d40b4cf3b5",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 14,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "R$ 2.088,00",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": 2088,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 2,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 464610,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "f6c0401698a456ef172dc9215f1c8972",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "1 parcela",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "1 parcela",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "1 parcela",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "2 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "3 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "4 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "5 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "6 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "7 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "8 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "9 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "10 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "11 parcelas *",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "12 parcelas *",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "13 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "14 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "15 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "16 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "17 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "18 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "19 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "20 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "21 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "22 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "23 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "24 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Sem parcelas"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": "1 parcela",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "1 parcela"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 466497,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Valor Entrada",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "4fd1200ca0f2ecab18a39a67e0fd71ae",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 14,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "R$ 2.088,00",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": 2088,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 2,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 213680,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Primeiro vencimento para",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "5173151fd5b13f52cb57e6bfa508cbf6",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 10,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "11/06/2025",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "2025-06-11",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 538628,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Observações PROTOCOLO",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "2b4be9376c646a7336c5c55db028e754",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 5,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Seguir normalmente.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Seguir normalmente.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 661507,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Observações sobre o paracer",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "66587c5ebbd07d7b17409efe2caff782",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 5,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Tem certo risco pela marca abaixo (tacchino), porém entendo que na forma mista será viável.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Tem certo risco pela marca abaixo (tacchino), porém entendo que na forma mista será viável.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 661502,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Teve auxílio para a análise de viabilidade?",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "8034c42baa166712abc332f29a1bf593",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 6,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Leandro"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "[\"Leandro\"]",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Não",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Leandro",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Kadu",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Amauri"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Leandro"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Leandro"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 228249,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Status do logotipo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "86ab312e990425e404ac32064dfecfa8",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Cliente não enviou, solicitar",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Cliente não enviou, solicitar",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Cliente enviou, está na conversa",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Cliente não enviou, solicitar",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Logo está em desenvolvimento para protocolo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Nominativo 🚨 Atenção redobrada nos campos de nome da marca",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Outro ⚠️ Descrever no campo de observações"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": "Cliente não enviou, solicitar",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Cliente não enviou, solicitar"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 225853,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Estado das especificações",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "9ffcbfa84e2a3aa8337170de90ba91f6",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Definidas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Definidas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Definidas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Validar com cliente",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Aprofundar",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "❇️ Livre preenchimento"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": "Definidas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Definidas"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 225868,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Titularidade / Faturamento",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "5bd45571eadd6347eb7e07a66d213163",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Pessoa Física",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Pessoa Física",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Pessoa Jurídica",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Pessoa Física",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Multi-titularidade / Faturamento PJ",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Multi-titularidade / Faturamento CPF"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": "Pessoa Física",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Pessoa Física"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 664183,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Condição de Pagamento Especial",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "400c20279e00e288b2b1f6f72306d13a",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 5,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 258486,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Descrição para contrato de recurso administrativo. (Ver exemplo na descrição)",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "420736e1032255b644a19be03ed75dbf",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 5,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 679153,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Tipo de contrato",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "36258b10085b9dd9ce1c12ea12b27043",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Completo - CPF - À vista [Registro]",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Completo - CPF - À vista [Registro]",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 679327,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Link Contrato",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "9760c2f315fa8405ea0a20faa429b7dd",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 9,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "https://app.clicksign.com/notarial/compat/requests/f99d0f57-b41e-42c2-be29-d7beab390082",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "https://app.clicksign.com/notarial/compat/requests/f99d0f57-b41e-42c2-be29-d7beab390082",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 679956,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Link Procuração Assinada",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "3a91610a5f52128da59465aba306d8de",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 9,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "https://signature.registrese.app.br/documents/48743676/Procuracao_Tacchio-assinado.pdf",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "https://signature.registrese.app.br/documents/48743676/Procuracao_Tacchio-assinado.pdf",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 679292,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Link Contrato Assinado",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "51b4824e916e5c74b117507165be803b",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 5,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "https://signature.registrese.app.br/documents/48743676/Contrato_de_Assessoria_Tacchio-assinado.pdf",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "https://signature.registrese.app.br/documents/48743676/Contrato_de_Assessoria_Tacchio-assinado.pdf",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 693250,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Anotações:",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "b9f8f6a6c2502b6943f77c8e42281086",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 5,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 194249,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Número GRU",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "fdbb4b72dafb35b5a370be9f94c85d05",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "29409172338902398",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "29409172338902398",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 194250,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Número Processo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "b11d4e52401560bfe06dfb3f4283e993",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "939551373",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "939551373",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 587550,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "LinkProtocolo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "a1a4617305b87488d52e104f35291aaf",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 9,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "https://www.dropbox.com/scl/fi/v6n5d14nxbucv1jfltge3/TACCHIO-GELATO-ARTIGIANALE-PROTOCOLO-CLASSE-43-939551373.pdf?rlkey=0d6z37rxasgerdrzfib5w6kil&dl=0",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "https://www.dropbox.com/scl/fi/v6n5d14nxbucv1jfltge3/TACCHIO-GELATO-ARTIGIANALE-PROTOCOLO-CLASSE-43-939551373.pdf?rlkey=0d6z37rxasgerdrzfib5w6kil&dl=0",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 587684,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Data Depósito",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "741932b9fbe261dbfeb73663657fd41f",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 10,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "12/06/2025",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "2025-06-12",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 577128,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Movimentações",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "af2052986dac0e51a3fa29edb39079f7",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 6,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Oposição",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Manifestação",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Exigência",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Desistência"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     }
17|api-v3-rgsys  | 2025-06-12 08:42:13:   ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "person": {
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "id": ********,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "account_id": 13955,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "company_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "city_id": 3771,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "owner_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "manager_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "cpf": "360.727.048-16",
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "avatar": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "name": "Murillo Christino Gomes",
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "hash": "rsj9jolqzm3xois1mgusj7z9wdl4fp0",
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "website": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "job_title": "Cargo não informado",
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "gender": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "birth_day": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "observation": "Pessoa cadastrada via integração JSON.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "customer_at": "2025-06-11",
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "facebook": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "linkedin": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "address_postal_code": "13320020",
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "address": "Rua José Revel",
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "address_number": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "address_complement": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "district": "Centro",
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "lat": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "lng": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "external_code": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "status": true,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "foreign_contact": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "data_legal_basis_processing": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "data_legal_origin_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "updated_at": "2025-06-12 08:29:01",
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "created_at": "2025-06-06 11:52:18",
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "deleted_at": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "lgpd_declaration_accepted": false,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "contactEmails": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:       {
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "id": ********,
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "account_id": 13955,
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "company_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "person_id": ********,
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "type": "Normal",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "email": "<EMAIL>",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "is_main": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "email_nf": false
17|api-v3-rgsys  | 2025-06-12 08:42:13:       }
17|api-v3-rgsys  | 2025-06-12 08:42:13:     ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "contactPhones": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:       {
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "id": ********,
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "account_id": 13955,
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "company_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "person_id": ********,
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "type": "Normal",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "phone": "*************",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "is_main": 1
17|api-v3-rgsys  | 2025-06-12 08:42:13:       }
17|api-v3-rgsys  | 2025-06-12 08:42:13:     ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:   }
17|api-v3-rgsys  | 2025-06-12 08:42:13: }
17|api-v3-rgsys  | 2025-06-12 08:42:13: 🔍 DEBUG - Estrutura completa dos dados CRM: {
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "id": ********,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "hash": "9r9r3fgvv70o8048kggcoksoc04kcgk",
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "rdstation_reference": "(11) 97350-1535",
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "type_reference": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "reference": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "temperature": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "probability": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "account_id": 13955,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "pipeline_id": 56897,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "owner_id": 86126,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "stage_id": 341596,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "person_id": ********,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "company_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "lost_reason_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "origin_id": 566319,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "started_in_stage_id": 341590,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "created_at": "2025-06-11 17:27:11",
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "title": "[43] TACCHIO GELATO ARTIGIANALE",
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "description": "11:53 06/06 Integração: (11) 97350-1535",
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "observation": "11:53 06/06 Integração: (11) 97350-1535",
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "status": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "closed_at": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "reason_close": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "deleted": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "freezed": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "value": 2320,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "order": 2337,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "updated_at": "2025-06-12 08:29:14",
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "last_stage_updated_at": "2025-06-12 08:29:14",
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "value_mrr": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "probably_closed_at": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "last_contact_at": "2025-06-12 08:29:14",
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "stage_changed_at": "2025-06-12 08:29:13",
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "frozen_at": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "lead_time": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "customFields": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 244296,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "mobile_phone",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "022511644db7bc1d8b7b607ab750cedd",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "(11) 97350-1535",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "(11) 97350-1535",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 213657,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Nome da marca ✍️",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "40641d0630e125b85f1d91b2e469b2b4",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "TACCHIO Gelato Artigianale",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "TACCHIO Gelato Artigianale",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 627361,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Como nos conheceu",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "44eba4ee59f67419160ecd387d969728",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Indicação de um amigo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Indicação de um amigo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 225754,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Nome Lead",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "5e61ab1b7425f4ebae5b0b0aa0745227",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Murillo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Murillo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 547311,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Motivo do registro",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "f3d22c9aab2dbc4b38e70e11f764921a",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 594785,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Data e hora visita",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "b66315788c90e658c1571c7ca335a6e5",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 594786,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Data e hora conversão",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "b226951a1e722b87cb6bdf16e15893ef",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "2025-06-06T11:53:25-03:00",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "2025-06-06T11:53:25-03:00",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 613811,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Dispositivo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "3cbe466620723d4a0f2174e1d98a9d25",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 232453,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Canal",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "09ec28a498ad27023b130d8d1bac0230",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "/",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "/",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 388863,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Campanha",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "cf1ab73f8b0f611734b63f619ef95d73",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 631782,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "UTM Final",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "8cd3283b4af7d496cc84ff1f961207fe",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 480567,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "url_conversao",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "1f80ef556fd1adc9efb8a1b4f7d95031",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 5,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 631785,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Historico Campanhas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "c8ac93e1550815982f9030cd58b9d4f7",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 204059,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Lead Score",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "2efae4f52ad645c8fbbd483f586796f8",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "180",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "180",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 196895,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Indicado por",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "ff160b3013b1fb3818a9b250c689b339",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 623519,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Consentimento",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "b4c1336997972cb7722fc209e6e508c1",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "accepted",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "accepted",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 703558,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "mkt-trk",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "54a85cd08d7fc7f06963493dbf23acf3",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 5,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "{\"trfSrc\":\"\",\"rdTrk\":\"{\\\"id\\\":\\\"16ade25a-6814-446c-9c45-6934cb9556e1\\\"}\"}",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "{\"trfSrc\":\"\",\"rdTrk\":\"{\\\"id\\\":\\\"16ade25a-6814-446c-9c45-6934cb9556e1\\\"}\"}",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 229327,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": " Nome Lead",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "9ddab6c554b26f40c43f8adbc7d0814a",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Murillo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Murillo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 213595,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Estágio do negócio",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "b581f8cef83aaaa7332129dc2552dd3e",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Pretendo começar em breve",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Pretendo começar em breve",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Comecei recentemente",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Já atuo há mais de 6 meses",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Já atuo há mais de 2 anos",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Pretendo começar em breve",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Por enquanto é só um plano"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": "Pretendo começar em breve",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Pretendo começar em breve"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 370155,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "pipecanal",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "dbdb72e1e1947a63c4fc9f16af7836bf",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "/",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "/",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 521462,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "score",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "480ed6abb2be56750429335e98bbcbde",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "120",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "120",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 544091,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "fase-do-planejamento",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "383add8ec4fbeb309870653586e667cb",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Logotipo está pronto e quero registrar antes de iniciar a divulgação.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Logotipo está pronto e quero registrar antes de iniciar a divulgação.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Não tenho logotipo ainda, mas quero garantir a propriedade do nome.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Logotipo está pronto e quero registrar antes de iniciar a divulgação.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Confirmar se o nome está disponível, antes de criar o logotipo.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Somente saber se está disponível.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Se estiver disponível, quero registrar.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Verificar disponibilidade."
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": "Logotipo está pronto e quero registrar antes de iniciar a divulgação.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Logotipo está pronto e quero registrar antes de iniciar a divulgação."
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 244308,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Quantos colaboradores?",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "b0b3877ee5bde2ebc511f25210f4698c",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "1 a 2",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "1 a 2",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "1 a 2",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "3 a 5",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "6 a 10",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "11 a 50",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "+ de 50",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Mais de 50"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": "1 a 2",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "1 a 2"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 369377,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Possui CNPJ",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "83342876471f8508dd364202916d3b24",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Sim.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Sim.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Sim",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Não, mas já estamos providenciando",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Atuo como pessoa física",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Não, mas estamos providenciando.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Sim."
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": "Sim.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Sim."
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 213683,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "porte",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "3843ba7f86f1357c881fca480f1d0754",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 6,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Pessoa física"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "[\"Pessoa física\"]",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Pessoa física",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "MEI",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "ME",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "EPP",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Demais",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Inova Simples",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "CNPJ em criação",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Ainda não identificado",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Associação",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Não sei responder.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "DEMAIS"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Pessoa física"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Pessoa física"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 240584,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Tipo de negócio",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "f91331fcd8a32e775b1c7de2a4632ff5",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Outros",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Outros",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Vestuário",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Fabricação de produtos",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Comércio de produtos de outras marcas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Prestação de serviço",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Produtor de conteúdo ou produto digital",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Entidade social ou organização religiosa",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Outros",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Comércio de produtos de marca própria"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": "Outros",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Outros"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 196382,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Ramo de atuação",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "5e2ab920101b625d8fcc7d657bacae4c",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "gelateria",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "gelateria",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 244306,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "sua-marca-possui-site-ou-redes-sociais",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "d411c026484e16660fed999b270549c3",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Ainda não",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Ainda não",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 549882,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "IdOriginal",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "fe3516735e9a34b77b65a07a51255344",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 14,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "48743676",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": 48743676,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 594787,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Hora entrada lead",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "ccd495150cb952162f0b4342adc3a2a0",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "06/06/2025, 11:53:30",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "06/06/2025, 11:53:30",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 525692,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "PTime",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "5f009cc35dc1c8bdd8934a66e206732f",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "06/06/2025, 11:53:54",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "06/06/2025, 11:53:54",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 547312,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Grau de conhecimento",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "99903be8bba5e5a2081698321061b451",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 554713,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Em quanto tempo quer resolver",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "325820413d0fc4f14d4d1daa84feebcd",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 554712,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Quanto já investiu na marca",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "c10b17029a905dd53719e773936777a8",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 194463,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Classes ✍️",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "8b7244acfcc87d24fbeebb7286ab6423",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 6,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "43"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "[\"43\"]",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "1",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "2",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "3",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "4",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "5",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "6",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "7",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "8",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "9",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "10",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "11",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "12",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "13",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "14",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "15",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "16",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "17",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "18",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "19",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "20",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "21",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "22",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "23",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "24",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "25",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "26",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "27",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "28",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "29",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "30",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "31",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "32",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "33",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "34",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "35",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "36",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "37",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "38",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "39",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "40",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "41",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "42",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "43",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "44",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "45"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "43"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "43"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 194466,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Tipo de marca ✍️",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "9fac515f2eaffe31a354c86baed1cb9a",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 6,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Mista"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "[\"Mista\"]",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Nominativa",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Mista",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Figurativa"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Mista"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Mista"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 203714,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Site e/ou redes sociais",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "8626a4baa93f97d3bbf189ce631b1368",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 225833,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Tipo de serviço",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "f832a9f2ab8eca6ad2cf81cf15050ebb",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 6,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Registro de marca"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "[\"Registro de marca\"]",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Registro de marca",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Renovação de registro",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Atualização de registro",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Recurso administrativo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Manifestação à Oposição",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Contranotificação"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Registro de marca"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Registro de marca"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 225839,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Viabilidade de registro ✍️",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "fb4b71d1a007bcf589278560161ee30d",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Viável no conjunto misto",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Viável no conjunto misto",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Viável ✅",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Inviável 🚫",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Viável com restrições",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Viável em algumas classes",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Arriscado",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Viável no conjunto misto"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": "Viável no conjunto misto",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Viável no conjunto misto"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 474111,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Estratégia de atendimento e diagnóstico ✍️",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "ef2dfea143f848b044a09538a4a4a2a8",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 5,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Tem certo risco pela marca abaixo (tacchino), porém entendo que na forma mista será viável.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Tem certo risco pela marca abaixo (tacchino), porém entendo que na forma mista será viável.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 228243,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Como foi atendido? ✍️",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "a5236860f6af80fc755fb8be5b159c7d",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 6,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "WhatsApp"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "[\"WhatsApp\"]",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Ligação Telefônica",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "WhatsApp",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "E-mail",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Video conferência",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Presencial",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "PDF",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Impressa",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Instagram",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Linkedin",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Outra rede social"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "WhatsApp"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "WhatsApp"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 213682,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Negociação ✍️",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "8fde08d168df09b1f7b785d15ee90568",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Valor normal",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Valor normal",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Valor normal",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Valor promocional"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": "Valor normal",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Valor normal"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 512979,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Análise de Crédito ✍️",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "5e62d130552affa0e8e73b6f8387a554",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Não é necessário realizar a análise de crédito.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Não é necessário realizar a análise de crédito.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Recusado",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Arriscado - Necessário confirmar pagamento da entrada para que possamos realizar o protocolo.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Aprovado, pagamento de entrada em até 7 dias, protocolo padrão.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Aprovado ✚ Pagamento de entrada a combinar, protocolo padrão.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Não é necessário realizar a análise de crédito."
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": "Não é necessário realizar a análise de crédito.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Não é necessário realizar a análise de crédito."
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 213659,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Proposta no Qwril ✍️",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "33ff940ed3cc66bc790db9a913ce08f6",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Sim",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Não"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 213684,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Link Qwirl",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "236fb94f5d58a794692a3415ef35adf0",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 9,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 226320,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Especificações",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "b6c2ad9120aaf13d3e8374bd3d43ae8d",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 5,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Serviços de lanchonetes; \nServiços de restaurantes; bar",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Serviços de lanchonetes; \nServiços de restaurantes; bar",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 713085,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Parceiro",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "1f2bfb8f0db126c85ed4ef5f56704730",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "08 Design | Anderiquei",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Feitoria | Alex Reuter",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Bradda",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Cristalize Cosméticos",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Emoticomm",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Firmorama",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Floema | Gi Marques",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Juliana Amaral",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Lai Creative | Lais",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "GGE.Art Design | George Varela",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Daniel Nardes | Designer",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Duora | Ana Paula Rubini",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Lucas Balbinot | Designer",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Luis Ferreira | Designer",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Marco Buka | Designer",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Studio Flamma",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Thiago Boller | Designer",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Contasul | Jacira",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Aline Portela | Designer",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Adston Bazante | Designer",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Gransolução Assessoria Contábil | Breno",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Contabilizei",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "DÉDI Agência Criativa | Deise Santiago Bento",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "EMCAT Contabilidade | Edimilson Silva",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Japona Studio | Lucas Mujo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Marchezi Design | Jonathan",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Juninho Menegatti",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Maxseller | Rogério",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Tenório Redator",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Cameleum | Vinicius Tadeu"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 545920,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Plano",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "04462ea78bc1a4ecc2e76ac7129c5be9",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Pacote completo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Pacote completo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Pacote simples",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Pacote completo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Não se aplica",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Plano especial 🪄"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": "Pacote completo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Pacote completo"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 464564,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Protocolo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "ca9c59b7f0edc9abd6840c84ec16980b",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 6,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Padrão: protocolo depois do faturamento"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "[\"Padrão: protocolo depois do faturamento\"]",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Deluxe: protocolo autorizado",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Padrão: protocolo depois do faturamento",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "24h: faturamento e protocolo em até 24h",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Arriscado: protocolo somente após a confirmação do recebimento"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Padrão: protocolo depois do faturamento"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Padrão: protocolo depois do faturamento"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 538627,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Observações FATURAMENTO",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "b6e1ef5dff432ef3beb1423efcfa202c",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 5,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Seguir normalmente",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Seguir normalmente",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 464609,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Forma de pagamento",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "02735c2f9f01ea613f9d61a9650b7aa0",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "À vista",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "À vista",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "À vista",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Boleto Bancário",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Cartão de Crédito",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Condição Especial ✴️"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": "À vista",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "À vista"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 213681,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Valor a cobrar",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "452e46be16581bc793d3d5d40b4cf3b5",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 14,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "R$ 2.088,00",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": 2088,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 2,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 464610,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "f6c0401698a456ef172dc9215f1c8972",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "1 parcela",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "1 parcela",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "1 parcela",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "2 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "3 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "4 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "5 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "6 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "7 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "8 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "9 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "10 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "11 parcelas *",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "12 parcelas *",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "13 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "14 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "15 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "16 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "17 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "18 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "19 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "20 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "21 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "22 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "23 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "24 parcelas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Sem parcelas"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": "1 parcela",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "1 parcela"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 466497,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Valor Entrada",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "4fd1200ca0f2ecab18a39a67e0fd71ae",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 14,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "R$ 2.088,00",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": 2088,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 2,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 213680,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Primeiro vencimento para",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "5173151fd5b13f52cb57e6bfa508cbf6",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 10,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "11/06/2025",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "2025-06-11",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 538628,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Observações PROTOCOLO",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "2b4be9376c646a7336c5c55db028e754",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 5,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Seguir normalmente.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Seguir normalmente.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 661507,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Observações sobre o paracer",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "66587c5ebbd07d7b17409efe2caff782",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 5,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Tem certo risco pela marca abaixo (tacchino), porém entendo que na forma mista será viável.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Tem certo risco pela marca abaixo (tacchino), porém entendo que na forma mista será viável.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 661502,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Teve auxílio para a análise de viabilidade?",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "8034c42baa166712abc332f29a1bf593",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 6,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Leandro"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "[\"Leandro\"]",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Não",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Leandro",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Kadu",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Amauri"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Leandro"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Leandro"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 228249,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Status do logotipo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "86ab312e990425e404ac32064dfecfa8",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Cliente não enviou, solicitar",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Cliente não enviou, solicitar",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Cliente enviou, está na conversa",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Cliente não enviou, solicitar",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Logo está em desenvolvimento para protocolo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Nominativo 🚨 Atenção redobrada nos campos de nome da marca",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Outro ⚠️ Descrever no campo de observações"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": "Cliente não enviou, solicitar",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Cliente não enviou, solicitar"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 225853,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Estado das especificações",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "9ffcbfa84e2a3aa8337170de90ba91f6",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Definidas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Definidas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Definidas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Validar com cliente",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Aprofundar",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "❇️ Livre preenchimento"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": "Definidas",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Definidas"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 225868,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Titularidade / Faturamento",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "5bd45571eadd6347eb7e07a66d213163",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 3,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Pessoa Física",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Pessoa Física",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Pessoa Jurídica",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Pessoa Física",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Multi-titularidade / Faturamento PJ",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Multi-titularidade / Faturamento CPF"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": "Pessoa Física",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Pessoa Física"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 664183,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Condição de Pagamento Especial",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "400c20279e00e288b2b1f6f72306d13a",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 5,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 258486,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Descrição para contrato de recurso administrativo. (Ver exemplo na descrição)",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "420736e1032255b644a19be03ed75dbf",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 5,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 679153,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Tipo de contrato",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "36258b10085b9dd9ce1c12ea12b27043",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "Completo - CPF - À vista [Registro]",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "Completo - CPF - À vista [Registro]",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 679327,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Link Contrato",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "9760c2f315fa8405ea0a20faa429b7dd",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 9,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "https://app.clicksign.com/notarial/compat/requests/f99d0f57-b41e-42c2-be29-d7beab390082",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "https://app.clicksign.com/notarial/compat/requests/f99d0f57-b41e-42c2-be29-d7beab390082",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 679956,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Link Procuração Assinada",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "3a91610a5f52128da59465aba306d8de",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 9,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "https://signature.registrese.app.br/documents/48743676/Procuracao_Tacchio-assinado.pdf",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "https://signature.registrese.app.br/documents/48743676/Procuracao_Tacchio-assinado.pdf",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 679292,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Link Contrato Assinado",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "51b4824e916e5c74b117507165be803b",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 5,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "https://signature.registrese.app.br/documents/48743676/Contrato_de_Assessoria_Tacchio-assinado.pdf",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "https://signature.registrese.app.br/documents/48743676/Contrato_de_Assessoria_Tacchio-assinado.pdf",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 693250,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Anotações:",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "b9f8f6a6c2502b6943f77c8e42281086",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 5,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 194249,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Número GRU",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "fdbb4b72dafb35b5a370be9f94c85d05",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "29409172338902398",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "29409172338902398",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 194250,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Número Processo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "b11d4e52401560bfe06dfb3f4283e993",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "939551373",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "939551373",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 587550,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "LinkProtocolo",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "a1a4617305b87488d52e104f35291aaf",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 9,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "https://www.dropbox.com/scl/fi/v6n5d14nxbucv1jfltge3/TACCHIO-GELATO-ARTIGIANALE-PROTOCOLO-CLASSE-43-939551373.pdf?rlkey=0d6z37rxasgerdrzfib5w6kil&dl=0",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "https://www.dropbox.com/scl/fi/v6n5d14nxbucv1jfltge3/TACCHIO-GELATO-ARTIGIANALE-PROTOCOLO-CLASSE-43-939551373.pdf?rlkey=0d6z37rxasgerdrzfib5w6kil&dl=0",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 587684,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Data Depósito",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "741932b9fbe261dbfeb73663657fd41f",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 10,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": "12/06/2025",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": "2025-06-12",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     },
17|api-v3-rgsys  | 2025-06-12 08:42:13:     {
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "id": 577128,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "name": "Movimentações",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "hash": "af2052986dac0e51a3fa29edb39079f7",
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "type": 6,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "belongs": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "raw_value": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "formula": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "output_type": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "decimal_places": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "allow_negative": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "currency_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "thousand_sep": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "options": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Oposição",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Manifestação",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Exigência",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "Desistência"
17|api-v3-rgsys  | 2025-06-12 08:42:13:       ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "selected_options": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:       "values_with_trash": null
17|api-v3-rgsys  | 2025-06-12 08:42:13:     }
17|api-v3-rgsys  | 2025-06-12 08:42:13:   ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:   "person": {
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "id": ********,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "account_id": 13955,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "company_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "city_id": 3771,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "owner_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "manager_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "cpf": "360.727.048-16",
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "avatar": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "name": "Murillo Christino Gomes",
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "hash": "rsj9jolqzm3xois1mgusj7z9wdl4fp0",
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "website": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "job_title": "Cargo não informado",
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "gender": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "birth_day": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "observation": "Pessoa cadastrada via integração JSON.",
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "customer_at": "2025-06-11",
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "facebook": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "linkedin": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "address_postal_code": "13320020",
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "address": "Rua José Revel",
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "address_number": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "address_complement": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "district": "Centro",
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "lat": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "lng": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "external_code": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "status": true,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "foreign_contact": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "data_legal_basis_processing": 1,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "data_legal_origin_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "updated_at": "2025-06-12 08:29:01",
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "created_at": "2025-06-06 11:52:18",
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "deleted_at": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "lgpd_declaration_accepted": false,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "contactEmails": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:       {
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "id": ********,
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "account_id": 13955,
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "company_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "person_id": ********,
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "type": "Normal",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "email": "<EMAIL>",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "is_main": 0,
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "email_nf": false
17|api-v3-rgsys  | 2025-06-12 08:42:13:       }
17|api-v3-rgsys  | 2025-06-12 08:42:13:     ],
17|api-v3-rgsys  | 2025-06-12 08:42:13:     "contactPhones": [
17|api-v3-rgsys  | 2025-06-12 08:42:13:       {
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "id": ********,
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "account_id": 13955,
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "company_id": null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "person_id": ********,
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "type": "Normal",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "phone": "*************",
17|api-v3-rgsys  | 2025-06-12 08:42:13:         "is_main": 1
17|api-v3-rgsys  | 2025-06-12 08:42:13:       }
17|api-v3-rgsys  | 2025-06-12 08:42:13:     ]
17|api-v3-rgsys  | 2025-06-12 08:42:13:   }
17|api-v3-rgsys  | 2025-06-12 08:42:13: }
17|api-v3-rgsys  | 2025-06-12 08:42:13: 👤 Person existe: true
17|api-v3-rgsys  | 2025-06-12 08:42:13: 📱 Person contactPhones: [
17|api-v3-rgsys  | 2025-06-12 08:42:13:   {
17|api-v3-rgsys  | 2025-06-12 08:42:13:     id: ********,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     account_id: 13955,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     company_id: null,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     person_id: ********,
17|api-v3-rgsys  | 2025-06-12 08:42:13:     type: 'Normal',
17|api-v3-rgsys  | 2025-06-12 08:42:13:     phone: '*************',
17|api-v3-rgsys  | 2025-06-12 08:42:13:     is_main: 1
17|api-v3-rgsys  | 2025-06-12 08:42:13:   }
17|api-v3-rgsys  | 2025-06-12 08:42:13: ]
17|api-v3-rgsys  | 2025-06-12 08:42:13: 📱 Encontrados 1 telefones da pessoa
17|api-v3-rgsys  | 2025-06-12 08:42:13: 📞 Processando telefone pessoa: *************
17|api-v3-rgsys  | 2025-06-12 08:42:13: 🔄 EXTRAÇÃO - Input: "*************"
17|api-v3-rgsys  | 2025-06-12 08:42:13: 🧹 EXTRAÇÃO - Número limpo: "*************" (13 dígitos)
17|api-v3-rgsys  | 2025-06-12 08:42:13: ✅ EXTRAÇÃO - Resultado final: "*************" → "**********"
17|api-v3-rgsys  | 2025-06-12 08:42:13: 🎯 Identificador gerado: ************* → **********
17|api-v3-rgsys  | 2025-06-12 08:42:13: ✅ Identificador válido adicionado: **********
17|api-v3-rgsys  | 2025-06-12 08:42:13: 🏢 Company existe: false
17|api-v3-rgsys  | 2025-06-12 08:42:13: 📱 Company contactPhones: undefined
17|api-v3-rgsys  | 2025-06-12 08:42:13: ❌ Nenhum telefone encontrado na empresa
17|api-v3-rgsys  | 2025-06-12 08:42:13: 📊 Total de identificadores válidos: 1
17|api-v3-rgsys  | 2025-06-12 08:42:13: 📊 Identificadores finais: [**********]
17|api-v3-rgsys  | 2025-06-12 08:42:13: 📱 Identificadores gerados: **********
17|api-v3-rgsys  | 2025-06-12 08:42:13: 🔄 EXTRAÇÃO - Input: "*************"
17|api-v3-rgsys  | 2025-06-12 08:42:13: 🧹 EXTRAÇÃO - Número limpo: "*************" (13 dígitos)
17|api-v3-rgsys  | 2025-06-12 08:42:13: ✅ EXTRAÇÃO - Resultado final: "*************" → "**********"
17|api-v3-rgsys  | 2025-06-12 08:42:13: 🏷️ Campos personalizados: 73 campos inseridos como array
17|api-v3-rgsys  | 2025-06-12 08:42:13: 📱 Identificador final: **********
17|api-v3-rgsys  | 2025-06-12 08:42:13: ➕ Criando novo cliente: Murillo Christino Gomes
17|api-v3-rgsys  | 2025-06-12 08:42:13: 📇 Criando contatos para cliente ID: 7968
17|api-v3-rgsys  | 2025-06-12 08:42:13: ✅ Contato principal criado: ************* | <EMAIL>
17|api-v3-rgsys  | 2025-06-12 08:42:13: 🔗 Conectando cliente 7968 ao processo 939551373
17|api-v3-rgsys  | 2025-06-12 08:42:13: Erro ao gerar link: Error: Cliente não possui número de documento para gerar senha
17|api-v3-rgsys  | 2025-06-12 08:42:13:     at /root/apps/api-v3-rgsys/src/services/processamentoProtocoloService.ts:561:13
17|api-v3-rgsys  | 2025-06-12 08:42:13:     at Generator.next (<anonymous>)
17|api-v3-rgsys  | 2025-06-12 08:42:13:     at fulfilled (/root/apps/api-v3-rgsys/src/services/processamentoProtocoloService.ts:5:58)
17|api-v3-rgsys  | 2025-06-12 08:42:13: ❌ Erro no processamento do protocolo 939551373: Error: Cliente não possui número de documento para gerar senha
17|api-v3-rgsys  | 2025-06-12 08:42:13:     at /root/apps/api-v3-rgsys/src/services/processamentoProtocoloService.ts:561:13
17|api-v3-rgsys  | 2025-06-12 08:42:13:     at Generator.next (<anonymous>)
17|api-v3-rgsys  | 2025-06-12 08:42:13:     at fulfilled (/root/apps/api-v3-rgsys/src/services/processamentoProtocoloService.ts:5:58)
17|api-v3-rgsys  | 2025-06-12 08:42:13: ❌ Falha no protocolo 939551373: Error: Cliente não possui número de documento para gerar senha
17|api-v3-rgsys  | 2025-06-12 08:42:13:     at /root/apps/api-v3-rgsys/src/services/processamentoProtocoloService.ts:561:13
17|api-v3-rgsys  | 2025-06-12 08:42:13:     at Generator.next (<anonymous>)
17|api-v3-rgsys  | 2025-06-12 08:42:13:     at fulfilled (/root/apps/api-v3-rgsys/src/services/processamentoProtocoloService.ts:5:58)
17|api-v3-rgsys  | 2025-06-12 08:42:13: ✅ Job de processamento concluído
17|api-v3-rgsys  | 2025-06-12 08:43:00: 🔄 Iniciando job de processamento de protocolos...
17|api-v3-rgsys  | 2025-06-12 08:43:00: 📋 Nenhum protocolo pendente encontrado
17|api-v3-rgsys  | 2025-06-12 08:44:00: 🔄 Iniciando job de processamento de protocolos...
17|api-v3-rgsys  | 2025-06-12 08:44:00: 📋 Nenhum protocolo pendente encontrado